# 图谱增强测试查询功能改进

## 问题描述

原有的图谱增强设置页面的测试查询功能存在以下问题：

1. **输入参数过于复杂**：界面显示了很多针对LightRAG框架的参数（mode、top_k、chunk_top_k、response_type等），对于Graphiti框架用户来说过于复杂且不相关。

2. **参数不匹配**：Graphiti框架的search接口实际只需要简单的参数：
   - `query`: 查询内容（必需）
   - `group_ids`: 组ID列表（可选）
   - `max_facts`: 最大返回结果数（可选，默认10）

3. **用户体验差**：用户需要填写很多不相关的参数才能进行测试。

## 解决方案

### 1. 前端界面优化

**文件**: `frontend/src/pages/settings/GraphEnhancementSettingsPage.js`

**改进内容**:
- 根据框架类型动态显示不同的参数界面
- 对于Graphiti框架，只显示简化的参数：
  - 查询内容（必需）
  - 最大结果数（可选，默认10）
  - 组ID（可选，支持多个）
- 对于LightRAG等其他框架，保持原有的完整参数界面

**关键代码**:
```javascript
// 根据框架类型显示不同的参数界面
const isGraphiti = config?.framework === 'graphiti';

{isGraphiti ? (
  // Graphiti 框架的简化参数界面
  <Row gutter={16}>
    <Col span={12}>
      <Form.Item name="max_facts" label="最大结果数">
        <InputNumber 
          min={1} 
          max={100} 
          size="small" 
          style={{ width: '100%' }}
          placeholder="默认10"
        />
      </Form.Item>
    </Col>
    <Col span={12}>
      <Form.Item name="group_ids" label="组ID (可选)">
        <Select
          mode="tags"
          size="small"
          placeholder="输入组ID，支持多个"
          style={{ width: '100%' }}
          tokenSeparators={[',']}
        />
      </Form.Item>
    </Col>
  </Row>
) : (
  // LightRAG 等其他框架的完整参数界面
  // ... 原有的复杂参数界面
)}
```

### 2. 后端接口优化

**文件**: `backend/app/api/routes/graph_enhancement.py`

**改进内容**:
- 根据框架类型构建不同的查询参数
- 对于Graphiti框架，使用简化的参数结构
- 对于其他框架，保持原有的参数结构

**关键代码**:
```python
# 根据框架类型构建查询参数
if config.framework == 'graphiti':
    # Graphiti 框架参数
    query_params = {
        'max_facts': data.get('max_facts', 10),
        'group_ids': data.get('group_ids', [])
    }
else:
    # LightRAG 等其他框架参数
    query_params = {
        'mode': data.get('mode', config.default_query_mode),
        'top_k': data.get('top_k', config.top_k),
        'chunk_top_k': data.get('chunk_top_k', config.chunk_top_k),
        'response_type': data.get('response_type', 'Multiple Paragraphs')
    }
```

### 3. 图谱增强服务优化

**文件**: `backend/app/services/graph_enhancement_service.py`

**改进内容**:
- 优化`_query_graphiti_direct`方法，支持完整的Graphiti API参数
- 改进错误处理和日志记录
- 优化结果格式化，提供更好的用户体验

**关键改进**:
```python
def _query_graphiti_direct(self, config, query: str, params: Dict[str, Any]) -> Tuple[bool, Any]:
    """直接执行Graphiti查询 - 使用配置中的服务地址"""
    # 准备查询参数，支持Graphiti的完整参数
    query_data = {
        'query': query,
        'max_facts': params.get('max_facts', 10)
    }
    
    # 处理group_ids参数
    group_ids = params.get('group_ids', [])
    if group_ids and len(group_ids) > 0:
        # 过滤掉空字符串
        group_ids = [gid.strip() for gid in group_ids if gid and gid.strip()]
        if group_ids:
            query_data['group_ids'] = group_ids
        else:
            query_data['group_ids'] = None
    else:
        query_data['group_ids'] = None
    
    # 格式化结果
    if 'facts' in result:
        facts = result['facts']
        if facts:
            # 将facts格式化为可读的文本
            formatted_result = "\n\n".join([
                f"事实 {i+1}: {fact.get('fact', '')}\n时间: {fact.get('created_at', 'N/A')}"
                for i, fact in enumerate(facts)
            ])
            return True, formatted_result
        else:
            return True, "未找到相关信息"
```

## 使用方法

### 对于Graphiti框架用户

1. 进入图谱增强设置页面
2. 点击"测试查询"按钮
3. 在弹出的对话框中：
   - 输入查询内容（必需）
   - 设置最大结果数（可选，默认10）
   - 输入组ID（可选，支持多个，用逗号分隔）
4. 点击"执行查询"

### 对于其他框架用户

保持原有的使用方式，界面会显示完整的参数选项。

## 技术细节

### API调用流程

1. 前端发送POST请求到 `/graph-enhancement/test-query`
2. 后端根据框架类型构建相应的查询参数
3. 对于Graphiti框架，调用 `_query_graphiti_direct` 方法
4. 该方法构建标准的Graphiti API请求，发送到配置的服务地址的 `/search` 端点
5. 格式化返回结果并返回给前端

### Graphiti API格式

请求格式：
```json
{
  "query": "查询内容",
  "group_ids": ["group1", "group2"] | null,
  "max_facts": 10
}
```

响应格式：
```json
{
  "facts": [
    {
      "uuid": "fact-uuid",
      "fact": "事实内容",
      "created_at": "2025-01-01T00:00:00Z",
      "valid_at": "2025-01-01T00:00:00Z",
      "invalid_at": null
    }
  ]
}
```

## 优势

1. **简化用户体验**：Graphiti用户只需要关注相关的参数
2. **保持兼容性**：其他框架的用户体验不受影响
3. **标准化API调用**：直接使用Graphiti的标准API格式
4. **更好的错误处理**：提供详细的错误信息和日志
5. **灵活的参数处理**：支持可选参数的智能处理

## 测试建议

1. 测试Graphiti框架的简化界面
2. 测试LightRAG等其他框架的完整界面
3. 测试各种参数组合
4. 测试错误情况的处理
5. 验证API调用的正确性
