# ABM-LLM-v2 前端多语言国际化改造计划

## 项目概述

ABM-LLM-v2 是一个多智能体专家决策与执行系统，基于 React + Ant Design 前端构建。目前系统界面主要使用中文，需要进行前端国际化改造以支持多语言切换。

## 当前状态分析

### 前端技术栈
- **框架**: React 18
- **UI库**: Ant Design 5.26.6
- **路由**: React Router
- **HTTP客户端**: Axios
- **构建工具**: Create React App + Craco
- **包管理**: pnpm

### 现有国际化支持情况
- ❌ **界面文本**: 硬编码为中文，无国际化框架
- ❌ **Ant Design**: 未配置多语言支持
- ❌ **错误提示**: 前端错误消息为中文
- ❌ **表单验证**: 验证消息为中文
- ✅ **基础配置**: 已有环境变量配置支持

## 改造目标

### 支持语言
1. **中文 (zh-CN)** - 默认语言，保持现有功能
2. **英文 (en-US)** - 主要国际化目标
3. **预留扩展** - 为未来支持更多语言做准备

### 功能目标
- 前端界面完全多语言化
- 语言切换功能
- 语言偏好持久化
- Ant Design 组件多语言支持
- 响应式语言切换（无需刷新页面）

## 改造策略

### 阶段一：国际化基础设施搭建

#### 1.1 安装依赖包
```bash
cd frontend
pnpm add react-i18next i18next i18next-browser-languagedetector
```

#### 1.2 语言资源文件结构
```
frontend/src/locales/
├── index.js                 # i18n配置入口
├── zh-CN/
│   ├── index.js            # 中文资源汇总
│   ├── common.json         # 通用词汇
│   ├── navigation.json     # 导航菜单
│   ├── forms.json          # 表单标签和验证
│   ├── messages.json       # 提示消息
│   ├── pages/
│   │   ├── home.json       # 首页
│   │   ├── login.json      # 登录页
│   │   ├── roles.json      # 角色管理
│   │   ├── actionspace.json # 行动空间
│   │   ├── actiontask.json # 行动任务
│   │   ├── workspace.json  # 项目空间
│   │   └── settings.json   # 设置页面
│   └── components/
│       ├── layout.json     # 布局组件
│       ├── modals.json     # 模态框
│       └── tables.json     # 表格组件
└── en-US/
    └── [相同结构]
```

#### 1.3 i18n 配置
- 配置 i18next 基础设置
- 集成浏览器语言检测
- 设置语言切换机制
- 配置 Ant Design 国际化

### 阶段二：核心组件改造

#### 2.1 应用入口改造
- 修改 `src/index.js` 集成 i18n
- 配置 Ant Design ConfigProvider
- 设置默认语言和回退语言

#### 2.2 布局组件改造
- 改造 `MainLayout.js` 主布局
- 添加语言切换器到顶部导航
- 改造侧边栏导航菜单
- 改造面包屑导航

#### 2.3 核心页面改造
- 登录页面 (`Login.js`)
- 首页 (`Home.js`)
- 角色管理页面
- 行动空间管理页面
- 行动任务页面
- 项目空间页面
- 设置页面

### 阶段三：组件和功能完善

#### 3.1 表单组件改造
- 表单标签国际化
- 验证消息国际化
- 占位符文本国际化
- 帮助提示国际化

#### 3.2 表格和列表组件
- 表格列标题国际化
- 操作按钮国际化
- 状态显示国际化
- 分页组件国际化

#### 3.3 模态框和弹窗
- 确认对话框国际化
- 表单模态框国际化
- 提示消息国际化

### 阶段四：测试和优化

#### 4.1 语言切换功能
- 实现语言切换器组件
- 语言偏好本地存储
- 页面无刷新切换
- 语言状态全局管理

#### 4.2 本地化优化
- 日期时间格式本地化
- 数字格式本地化
- 文件大小显示本地化
- 相对时间显示本地化

## 实施计划

### 第1周：基础设施搭建
- [ ] 安装 react-i18next 相关依赖
- [ ] 创建完整的语言资源文件结构
- [ ] 配置 i18n 基础设置
- [ ] 集成 Ant Design 国际化支持

### 第2周：核心组件改造
- [ ] 改造应用入口和配置
- [ ] 改造主布局组件 (MainLayout)
- [ ] 实现语言切换器组件
- [ ] 改造导航和菜单组件

### 第3周：主要页面改造
- [ ] 改造登录页面
- [ ] 改造首页和仪表板
- [ ] 改造角色管理相关页面
- [ ] 改造行动空间管理页面

### 第4周：功能页面完善
- [ ] 改造行动任务相关页面
- [ ] 改造项目空间管理页面
- [ ] 改造系统设置页面
- [ ] 改造各类模态框和表单

### 第5周：测试和优化
- [ ] 完整功能测试
- [ ] 语言切换测试
- [ ] 界面适配优化
- [ ] 性能优化和文档更新

## 技术实现细节

### i18n 配置方案

#### 1. 基础配置文件
```javascript
// src/locales/index.js
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// 导入语言资源
import zhCN from './zh-CN';
import enUS from './en-US';

i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources: {
      'zh-CN': zhCN,
      'en-US': enUS,
    },
    fallbackLng: 'zh-CN',
    debug: process.env.NODE_ENV === 'development',
    interpolation: {
      escapeValue: false,
    },
    detection: {
      order: ['localStorage', 'navigator', 'htmlTag'],
      caches: ['localStorage'],
    },
  });

export default i18n;
```

#### 2. 语言资源结构
```javascript
// src/locales/zh-CN/index.js
import common from './common.json';
import navigation from './navigation.json';
import forms from './forms.json';
import messages from './messages.json';
import pages from './pages';
import components from './components';

export default {
  common,
  navigation,
  forms,
  messages,
  pages,
  components,
};
```

#### 3. Ant Design 国际化集成
```javascript
// src/App.js 修改
import { ConfigProvider } from 'antd';
import { useTranslation } from 'react-i18next';
import zhCN from 'antd/locale/zh_CN';
import enUS from 'antd/locale/en_US';

function App() {
  const { i18n } = useTranslation();

  const antdLocale = i18n.language === 'en-US' ? enUS : zhCN;

  return (
    <ConfigProvider locale={antdLocale}>
      {/* 应用内容 */}
    </ConfigProvider>
  );
}
```

#### 4. 语言切换器组件
```javascript
// src/components/common/LanguageSwitcher.js
import React from 'react';
import { Select, Space } from 'antd';
import { GlobalOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

const LanguageSwitcher = ({ size = 'middle' }) => {
  const { i18n, t } = useTranslation();

  const languages = [
    { value: 'zh-CN', label: '中文', flag: '🇨🇳' },
    { value: 'en-US', label: 'English', flag: '🇺🇸' },
  ];

  const handleChange = (value) => {
    i18n.changeLanguage(value);
  };

  return (
    <Select
      size={size}
      value={i18n.language}
      onChange={handleChange}
      suffixIcon={<GlobalOutlined />}
      style={{ minWidth: 120 }}
    >
      {languages.map(lang => (
        <Select.Option key={lang.value} value={lang.value}>
          <Space>
            <span>{lang.flag}</span>
            <span>{lang.label}</span>
          </Space>
        </Select.Option>
      ))}
    </Select>
  );
};

export default LanguageSwitcher;
```

#### 5. 使用示例
```javascript
// 在组件中使用翻译
import { useTranslation } from 'react-i18next';

const MyComponent = () => {
  const { t } = useTranslation();

  return (
    <div>
      <h1>{t('pages.home.title')}</h1>
      <Button>{t('common.save')}</Button>
      <p>{t('messages.success', { name: 'John' })}</p>
    </div>
  );
};
```

### 关键改造点

#### 1. 应用入口修改
```javascript
// src/index.js
import React from 'react';
import ReactDOM from 'react-dom/client';
import { BrowserRouter } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import './locales'; // 导入i18n配置
import App from './App';

// 其余代码保持不变
```

#### 2. 主布局组件改造
- 在顶部导航栏添加语言切换器
- 改造侧边栏菜单项
- 改造用户下拉菜单

#### 3. 表单验证消息
```javascript
// 表单验证规则国际化
const rules = {
  required: {
    required: true,
    message: t('forms.validation.required', { field: t('forms.fields.username') })
  }
};
```

## 风险评估和缓解策略

### 主要风险
1. **界面布局问题** - 不同语言文本长度差异可能影响布局
2. **性能影响** - 语言资源文件加载可能影响首屏性能
3. **维护复杂度** - 需要维护多套语言文件
4. **翻译质量** - 专业术语翻译准确性

### 缓解策略
1. **响应式设计** - 确保界面能适应不同长度的文本
2. **懒加载** - 按需加载语言资源，减少初始包大小
3. **自动化工具** - 使用工具检测缺失的翻译
4. **专业审核** - 邀请专业人员审核翻译质量

## 成功指标

### 技术指标
- [ ] 前端界面100%支持中英文切换
- [ ] 语言切换无需刷新页面
- [ ] 首屏加载时间增加不超过5%
- [ ] 所有现有功能正常工作

### 用户体验指标
- [ ] 语言切换响应时间<500ms
- [ ] 用户语言偏好正确保存和恢复
- [ ] 界面布局在不同语言下保持美观
- [ ] 翻译内容准确、专业

## 后续扩展计划

### 更多语言支持
- 日语 (ja-JP)
- 韩语 (ko-KR)
- 德语 (de-DE)
- 法语 (fr-FR)

### 高级功能
- 语言包动态加载
- 翻译内容在线编辑
- 翻译进度管理工具
- 社区翻译贡献系统

---

**注意**: 本计划专注于前端国际化改造，后端API响应保持现状。如需后端多语言支持，可作为后续独立项目进行。
