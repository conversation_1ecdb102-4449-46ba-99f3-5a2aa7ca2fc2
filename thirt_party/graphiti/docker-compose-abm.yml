services:
  graphiti-mcp:
    image: graphiti:latest
    ports:
      - "8000:8000"  # FastAPI service
      - "8001:8001"  # MCP SSE service
    depends_on:
      neo4j:
        condition: service_healthy
    environment:
      # Neo4j Database Configuration
      - NEO4J_URI=bolt://neo4j:${NEO4J_PORT:-7687}
      - NEO4J_USER=neo4j
      - NEO4J_PASSWORD=password
      # OpenAI Embedder Configuration
      - OPENAI_EMBEDDER_API_KEY=${OPENAI_EMBEDDER_API_KEY:-no-api-key}
      - OPENAI_EMBEDDER_MODEL_ID=${OPENAI_EMBEDDER_MODEL_ID:-bge-m3:latest}
      - OPENAI_EMBEDDER_DIMENSION=${OPENAI_EMBEDDER_DIMENSION:-1024}
      - OPENAI_EMBEDDER_API_URL=${OPENAI_EMBEDDER_API_URL:-http://*********:11434/v1}
      # OpenAI API Configuration
      - OPENAI_API_KEY=${OPENAI_API_KEY:-sk-e5f55f84693f4758bc3b9c3a9149d54e}
      - MODEL_NAME=${MODEL_NAME:-mistral-small3.2:24b}
      - SMALL_MODEL_NAME=${SMALL_MODEL_NAME:-mistral-small3.2:24b}
      - OPENAI_BASE_URL=${OPENAI_BASE_URL:-http://*********:11434/v1}
      - OPENAI_COMPATIBLE=${OPENAI_COMPATIBLE:-false}
      # Optional Configuration
      - GROUP_ID=${GROUP_ID}
      - SEMAPHORE_LIMIT=${SEMAPHORE_LIMIT:-10}
      - MCP_SERVER_HOST=0.0.0.0
      - PATH=/root/.local/bin:${PATH}
      # Server Port Configuration
      - API_SERVER_PORT=8000
      - MCP_SERVER_SSE_PORT=8001
      # Azure OpenAI Configuration (optional)
      #- AZURE_OPENAI_ENDPOINT=${AZURE_OPENAI_ENDPOINT}
      #- AZURE_OPENAI_API_VERSION=${AZURE_OPENAI_API_VERSION}
      #- AZURE_OPENAI_DEPLOYMENT_NAME=${AZURE_OPENAI_DEPLOYMENT_NAME}
      #- AZURE_OPENAI_EMBEDDING_API_VERSION=${AZURE_OPENAI_EMBEDDING_API_VERSION}
      #- AZURE_OPENAI_EMBEDDING_DEPLOYMENT_NAME=${AZURE_OPENAI_EMBEDDING_DEPLOYMENT_NAME}
      #- AZURE_OPENAI_USE_MANAGED_IDENTITY=${AZURE_OPENAI_USE_MANAGED_IDENTITY}
    command: ["python", "mcp_server/graphiti_mcp_server2.py", "--transport", "sse", "--host", "0.0.0.0", "--dual-server"]
    healthcheck:
      test:
        [
          "CMD",
          "python",
          "-c",
          "import urllib.request; urllib.request.urlopen('http://localhost:8000/healthcheck')",
        ]
      interval: 10s
      timeout: 5s
      retries: 3
  neo4j:
    image: neo4j:5.26.2
    healthcheck:
      test:
        [
          "CMD-SHELL",
          "wget -qO- http://localhost:${NEO4J_PORT:-7474} || exit 1",
        ]
      interval: 1s
      timeout: 10s
      retries: 10
      start_period: 3s
    ports:
      - "7474:7474" # HTTP
      - "${NEO4J_PORT:-7687}:${NEO4J_PORT:-7687}" # Bolt
    volumes:
      - neo4j_data:/data
    environment:
      - NEO4J_AUTH=neo4j/password

volumes:
  neo4j_data:
