#!/usr/bin/env python3
"""
数据库迁移脚本：为ModelConfig表添加is_default_rerank字段
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.extensions import db
from app.models import ModelConfig
from sqlalchemy import text

def add_rerank_default_field():
    """为ModelConfig表添加is_default_rerank字段"""
    try:
        # 检查字段是否已存在
        result = db.session.execute(text("PRAGMA table_info(model_configs)"))
        columns = [row[1] for row in result.fetchall()]
        
        if 'is_default_rerank' not in columns:
            print("添加is_default_rerank字段到model_configs表...")
            db.session.execute(text("ALTER TABLE model_configs ADD COLUMN is_default_rerank BOOLEAN DEFAULT 0"))
            db.session.commit()
            print("✓ is_default_rerank字段添加成功")
        else:
            print("✓ is_default_rerank字段已存在，跳过添加")
            
    except Exception as e:
        print(f"✗ 添加is_default_rerank字段失败: {e}")
        db.session.rollback()
        raise

if __name__ == '__main__':
    from app import create_app
    
    app = create_app()
    with app.app_context():
        print("开始数据库迁移：添加重排序模型默认字段...")
        add_rerank_default_field()
        print("数据库迁移完成！")
