# Web框架
Flask==3.1.0
Flask-SQLAlchemy==3.1.1
Flask-Cors==5.0.1
Flask-SocketIO==5.5.1
PyYAML==6.0.2
PyJWT>=2.0

python-multipart==0.0.20
python-jose[cryptography]==3.4.0 # Version from python-jose, cryptography==44.0.2
passlib[bcrypt]==1.7.4        # Version from passlib, bcrypt==4.3.0
python-dotenv==1.0.1
nest_asyncio==1.6.0

# 数据库
SQLAlchemy==2.0.39
alembic==1.15.1
redis==5.2.1

# WebSocket
# websockets==15.0.1

# API DOC
flask-restx
marshmallow<3.20.0
apispec

# 并发处理
eventlet==0.39.1
python-engineio==4.11.2
requests==2.32.3

# 时区处理
pytz==2025.2

# MCP工具
uv
mcp==1.6.0 # MCP Python SDK
httpx>=0.25.0 # Required for SSE client
#metatrader-mcp-server # Windows only

# TiDB向量数据库
tidb-vector[client]==0.0.9
pymysql==1.1.1
sentence-transformers==3.3.1
pymilvus==2.3.4
