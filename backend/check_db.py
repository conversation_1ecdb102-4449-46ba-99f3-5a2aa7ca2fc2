#!/usr/bin/env python3
import sqlite3
import json

def check_database():
    conn = sqlite3.connect('app.db')
    cursor = conn.cursor()
    
    print('=== 检查表结构 ===')
    cursor.execute('PRAGMA table_info(model_configs)')
    columns = cursor.fetchall()
    rerank_field_exists = False
    for col in columns:
        if col[1] == 'is_default_rerank':
            rerank_field_exists = True
            print(f'✓ 找到字段: {col[1]} {col[2]}')
    
    if not rerank_field_exists:
        print('✗ is_default_rerank 字段不存在')
        print('添加字段...')
        try:
            cursor.execute('ALTER TABLE model_configs ADD COLUMN is_default_rerank BOOLEAN DEFAULT 0')
            conn.commit()
            print('✓ is_default_rerank 字段添加成功')
        except Exception as e:
            print(f'✗ 添加字段失败: {e}')
    
    print('\n=== 检查模型数据 ===')
    cursor.execute('SELECT id, name, modalities, is_default_text, is_default_embedding FROM model_configs')
    models = cursor.fetchall()
    rerank_models = []
    for model in models:
        modalities_str = model[2] if model[2] else '[]'
        try:
            modalities = json.loads(modalities_str)
            if 'rerank_output' in modalities:
                rerank_models.append(model)
                print(f'✓ 重排序模型: ID={model[0]}, Name={model[1]}')
        except:
            pass
    
    if not rerank_models:
        print('✗ 没有找到重排序模型')
        print('添加示例重排序模型...')
        try:
            cursor.execute('''
                INSERT INTO model_configs 
                (name, provider, model_id, base_url, api_key, context_window, max_output_tokens, 
                 request_timeout, is_default, is_default_text, is_default_embedding, is_default_rerank,
                 modalities, capabilities, additional_params, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))
            ''', (
                'bge-reranker-v2-m3',
                'OpenAI',
                'bge-reranker-v2-m3',
                'https://api.openai.com/v1',
                'your-openai-api-key',
                8192,
                0,
                30,
                0,  # is_default
                0,  # is_default_text
                0,  # is_default_embedding
                0,  # is_default_rerank
                json.dumps(['rerank_input', 'rerank_output']),
                json.dumps([]),
                json.dumps({'max_documents': 100, 'score_threshold': 0.0})
            ))
            conn.commit()
            print('✓ 示例重排序模型添加成功')
        except Exception as e:
            print(f'✗ 添加模型失败: {e}')
    
    conn.close()

if __name__ == '__main__':
    check_database()
