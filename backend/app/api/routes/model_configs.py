"""
模型配置API路由

处理与模型配置相关的所有API请求
"""
from flask import Blueprint, request, jsonify
from app.models import ModelConfig, db
import json
import logging

# 创建Blueprint
model_bp = Blueprint('model_api', __name__)

# 设置日志
logger = logging.getLogger(__name__)

@model_bp.route('/model-configs', methods=['GET'])
def get_model_configs():
    """获取所有模型配置"""
    # 检查是否需要返回真实API密钥
    include_keys = request.args.get('include_api_keys', 'false').lower() == 'true'

    configs = ModelConfig.query.all()
    result = []

    for config in configs:
        # 如果不包含API密钥，则返回加密的密钥
        api_key = config.api_key if include_keys else None
        if api_key:
            api_key = api_key
        else:
            api_key = None if not config.api_key else "********"

        result.append({
            'id': config.id,
            'name': config.name,
            'provider': config.provider,
            'model_id': config.model_id,
            'base_url': config.base_url,
            'api_key': api_key,
            'context_window': config.context_window,
            'max_output_tokens': config.max_output_tokens,
            'request_timeout': config.request_timeout,
            'is_default': config.is_default,
            'is_default_text': getattr(config, 'is_default_text', False),
            'is_default_embedding': getattr(config, 'is_default_embedding', False),
            'modalities': config.modalities,
            'capabilities': config.capabilities,
            'additional_params': config.additional_params,
            'created_at': config.created_at.isoformat(),
            'updated_at': config.updated_at.isoformat()
        })

    return jsonify({'model_configs': result})

@model_bp.route('/model-configs/<int:config_id>', methods=['GET'])
def get_model_config(config_id):
    """获取特定模型配置详情"""
    # 检查是否需要返回真实API密钥
    include_keys = request.args.get('include_api_keys', 'false').lower() == 'true'

    config = ModelConfig.query.get(config_id)
    if not config:
        return jsonify({'error': 'Model configuration not found'}), 404

    # 如果不包含API密钥，则返回加密的密钥
    api_key = config.api_key if include_keys else None
    if api_key:
        api_key = api_key
    else:
        api_key = None if not config.api_key else "********"

    return jsonify({
        'id': config.id,
        'name': config.name,
        'provider': config.provider,
        'model_id': config.model_id,
        'base_url': config.base_url,
        'api_key': api_key,
        'context_window': config.context_window,
        'max_output_tokens': config.max_output_tokens,
        'request_timeout': config.request_timeout,
        'is_default': config.is_default,
        'is_default_text': getattr(config, 'is_default_text', False),
        'is_default_embedding': getattr(config, 'is_default_embedding', False),
        'modalities': config.modalities,
        'capabilities': config.capabilities,
        'additional_params': config.additional_params,
        'created_at': config.created_at.isoformat(),
        'updated_at': config.updated_at.isoformat()
    })

@model_bp.route('/model-configs', methods=['POST'])
def create_model_config():
    """创建新模型配置"""
    data = request.get_json()

    # 验证必填字段
    required_fields = ['name', 'provider', 'model_id']
    for field in required_fields:
        if field not in data:
            return jsonify({'error': f'Missing required field: {field}'}), 400

    # 验证数值字段的范围
    if 'context_window' in data:
        context_window = data.get('context_window')
        if context_window is not None and context_window < 1:
            return jsonify({'error': 'context_window must be at least 1'}), 400

    if 'max_output_tokens' in data:
        max_output_tokens = data.get('max_output_tokens')
        if max_output_tokens is not None and max_output_tokens < 1:
            return jsonify({'error': 'max_output_tokens must be at least 1'}), 400

    if 'request_timeout' in data:
        request_timeout = data.get('request_timeout')
        if request_timeout is not None and (request_timeout < 10 or request_timeout > 300):
            return jsonify({'error': 'request_timeout must be between 10 and 300 seconds'}), 400

    # 如果设置为默认文本生成模型，需要更新其他文本生成模型
    if data.get('is_default_text'):
        existing_text_defaults = ModelConfig.query.filter_by(is_default_text=True).all()
        for model in existing_text_defaults:
            model.is_default_text = False
        db.session.commit()

    # 如果设置为默认嵌入模型，需要更新其他嵌入模型
    if data.get('is_default_embedding'):
        existing_embedding_defaults = ModelConfig.query.filter_by(is_default_embedding=True).all()
        for model in existing_embedding_defaults:
            model.is_default_embedding = False
        db.session.commit()

    # 创建新的模型配置
    new_config = ModelConfig(
        name=data['name'],
        provider=data['provider'],
        model_id=data['model_id'],
        base_url=data.get('base_url'),
        api_key=data.get('api_key'),
        context_window=data.get('context_window', 16000),
        max_output_tokens=data.get('max_output_tokens', 2000),
        request_timeout=data.get('request_timeout', 60),
        is_default=False,  # 废弃旧的is_default字段
        is_default_text=data.get('is_default_text', False),
        is_default_embedding=data.get('is_default_embedding', False),
        modalities=data.get('modalities', []),
        capabilities=data.get('capabilities', []),
        additional_params=data.get('additional_params', {})
    )

    db.session.add(new_config)
    db.session.commit()

    return jsonify({
        'id': new_config.id,
        'name': new_config.name,
        'provider': new_config.provider,
        'model_id': new_config.model_id,
        'is_default': new_config.is_default,
        'modalities': new_config.modalities,
        'capabilities': new_config.capabilities,
        'created_at': new_config.created_at.isoformat()
    }), 201

@model_bp.route('/model-configs/<int:config_id>', methods=['PUT'])
def update_model_config(config_id):
    """更新模型配置"""
    config = ModelConfig.query.get(config_id)
    if not config:
        return jsonify({'error': 'Model configuration not found'}), 404

    data = request.get_json()

    # 验证数值字段的范围
    if 'context_window' in data:
        context_window = data.get('context_window')
        if context_window is not None and context_window < 1:
            return jsonify({'error': 'context_window must be at least 1'}), 400

    if 'max_output_tokens' in data:
        max_output_tokens = data.get('max_output_tokens')
        if max_output_tokens is not None and max_output_tokens < 1:
            return jsonify({'error': 'max_output_tokens must be at least 1'}), 400

    if 'request_timeout' in data:
        request_timeout = data.get('request_timeout')
        if request_timeout is not None and (request_timeout < 10 or request_timeout > 300):
            return jsonify({'error': 'request_timeout must be between 10 and 300 seconds'}), 400

    # 如果设置为默认文本生成模型，需要更新其他文本生成模型
    if data.get('is_default_text') and not getattr(config, 'is_default_text', False):
        existing_text_defaults = ModelConfig.query.filter_by(is_default_text=True).all()
        for model in existing_text_defaults:
            model.is_default_text = False
        db.session.commit()

    # 如果设置为默认嵌入模型，需要更新其他嵌入模型
    if data.get('is_default_embedding') and not getattr(config, 'is_default_embedding', False):
        existing_embedding_defaults = ModelConfig.query.filter_by(is_default_embedding=True).all()
        for model in existing_embedding_defaults:
            model.is_default_embedding = False
        db.session.commit()

    # 更新模型配置
    for key, value in data.items():
        if hasattr(config, key) and key != 'id':
            # 废弃旧的is_default字段，不允许设置
            if key == 'is_default':
                continue
            setattr(config, key, value)

    db.session.commit()

    return jsonify({
        'id': config.id,
        'name': config.name,
        'provider': config.provider,
        'model_id': config.model_id,
        'base_url': config.base_url,
        'context_window': config.context_window,
        'max_output_tokens': config.max_output_tokens,
        'request_timeout': config.request_timeout,
        'is_default': config.is_default,
        'is_default_text': getattr(config, 'is_default_text', False),
        'is_default_embedding': getattr(config, 'is_default_embedding', False),
        'modalities': config.modalities,
        'capabilities': config.capabilities,
        'updated_at': config.updated_at.isoformat()
    })

@model_bp.route('/model-configs/<int:config_id>', methods=['DELETE'])
def delete_model_config(config_id):
    """删除模型配置"""
    config = ModelConfig.query.get(config_id)
    if not config:
        return jsonify({'error': 'Model configuration not found'}), 404

    # 如果删除的是默认模型，检查是否有其他模型可以设为默认
    if config.is_default:
        other_models = ModelConfig.query.filter(ModelConfig.id != config_id).all()
        if other_models:
            other_models[0].is_default = True

    db.session.delete(config)
    db.session.commit()

    return jsonify({'message': 'Model configuration deleted successfully'})

@model_bp.route('/model-configs/<int:config_id>/set-default', methods=['POST'])
def set_default_model(config_id):
    """设置默认模型配置（已废弃，保留向后兼容）"""
    return jsonify({'error': 'This API is deprecated. Please use /model-configs/set-defaults instead.'}), 400

@model_bp.route('/model-configs/set-defaults', methods=['POST'])
def set_default_models():
    """设置默认模型配置（支持分别设置文本生成、嵌入和重排序模型）"""
    data = request.get_json()

    if not data:
        return jsonify({'error': 'No data provided'}), 400

    text_model_id = data.get('text_model_id')
    embedding_model_id = data.get('embedding_model_id')
    rerank_model_id = data.get('rerank_model_id')

    if not text_model_id and not embedding_model_id and not rerank_model_id:
        return jsonify({'error': 'At least one model type must be specified'}), 400

    try:
        # 设置默认文本生成模型
        if text_model_id:
            text_config = ModelConfig.query.get(text_model_id)
            if not text_config:
                return jsonify({'error': f'Text model configuration {text_model_id} not found'}), 404

            # 检查模型是否支持文本输出
            modalities = text_config.modalities or []
            if 'text_output' not in modalities:
                return jsonify({'error': 'Selected text model does not support text output'}), 400

            # 清除其他模型的默认文本生成标记
            all_configs = ModelConfig.query.all()
            for model in all_configs:
                if hasattr(model, 'is_default_text'):
                    model.is_default_text = (model.id == text_model_id)

        # 设置默认嵌入模型
        if embedding_model_id:
            embedding_config = ModelConfig.query.get(embedding_model_id)
            if not embedding_config:
                return jsonify({'error': f'Embedding model configuration {embedding_model_id} not found'}), 404

            # 检查模型是否支持向量输出
            modalities = embedding_config.modalities or []
            if 'vector_output' not in modalities:
                return jsonify({'error': 'Selected embedding model does not support vector output'}), 400

            # 清除其他模型的默认嵌入标记
            all_configs = ModelConfig.query.all()
            for model in all_configs:
                if hasattr(model, 'is_default_embedding'):
                    model.is_default_embedding = (model.id == embedding_model_id)

        # 设置默认重排序模型
        if rerank_model_id:
            rerank_config = ModelConfig.query.get(rerank_model_id)
            if not rerank_config:
                return jsonify({'error': f'Rerank model configuration {rerank_model_id} not found'}), 404

            # 检查模型是否支持重排序输出
            modalities = rerank_config.modalities or []
            if 'rerank_output' not in modalities:
                return jsonify({'error': 'Selected rerank model does not support rerank output'}), 400

            # 清除其他模型的默认重排序标记
            all_configs = ModelConfig.query.all()
            for model in all_configs:
                if hasattr(model, 'is_default_rerank'):
                    model.is_default_rerank = (model.id == rerank_model_id)

        db.session.commit()

        result = {'message': 'Default models updated successfully'}
        if text_model_id:
            result['text_model'] = {
                'id': text_config.id,
                'name': text_config.name
            }
        if embedding_model_id:
            result['embedding_model'] = {
                'id': embedding_config.id,
                'name': embedding_config.name
            }
        if rerank_model_id:
            result['rerank_model'] = {
                'id': rerank_config.id,
                'name': rerank_config.name
            }

        return jsonify(result)

    except Exception as e:
        db.session.rollback()
        logger.error(f"设置默认模型失败: {e}")
        return jsonify({'error': f'Failed to set default models: {str(e)}'}), 500

@model_bp.route('/model-configs/defaults', methods=['GET'])
def get_default_models():
    """获取当前的默认模型配置"""
    try:
        # 获取默认文本生成模型
        text_model = ModelConfig.query.filter_by(is_default_text=True).first()

        # 获取默认嵌入模型
        embedding_model = ModelConfig.query.filter_by(is_default_embedding=True).first()

        # 获取默认重排序模型
        rerank_model = ModelConfig.query.filter_by(is_default_rerank=True).first()

        # 如果没有设置默认文本生成模型，查找第一个支持文本输出的模型
        if not text_model:
            text_models = ModelConfig.query.filter(
                ModelConfig.modalities.contains('text_output')
            ).all()
            if text_models:
                text_model = text_models[0]

        result = {}

        if text_model:
            result['text_model'] = {
                'id': text_model.id,
                'name': text_model.name,
                'provider': text_model.provider,
                'model_id': text_model.model_id
            }

        if embedding_model:
            result['embedding_model'] = {
                'id': embedding_model.id,
                'name': embedding_model.name,
                'provider': embedding_model.provider,
                'model_id': embedding_model.model_id
            }

        if rerank_model:
            result['rerank_model'] = {
                'id': rerank_model.id,
                'name': rerank_model.name,
                'provider': rerank_model.provider,
                'model_id': rerank_model.model_id
            }

        return jsonify(result)

    except Exception as e:
        logger.error(f"获取默认模型失败: {e}")
        return jsonify({'error': f'Failed to get default models: {str(e)}'}), 500

@model_bp.route('/model-configs/<int:config_id>/has-api-key', methods=['GET'])
def check_api_key(config_id):
    """检查模型配置是否有API密钥"""
    config = ModelConfig.query.get(config_id)
    if not config:
        return jsonify({'error': 'Model configuration not found'}), 404

    has_key = bool(config.api_key)

    return jsonify({
        'id': config.id,
        'has_api_key': has_key
    })

@model_bp.route('/model-configs/<int:config_id>/test-stream', methods=['POST'])
def sse_test_model_config(config_id):
    """使用SSE (Server-Sent Events) 提供流式模型测试API"""
    import queue
    import threading
    import sys
    import time
    import json
    from flask import Response, stream_with_context

    from app.models import ModelConfig
    from app.services.conversation.model_client import ModelClient

    # 获取模型配置
    config = ModelConfig.query.get(config_id)
    if not config:
        return jsonify({'error': 'Model configuration not found'}), 404

    # 检查URL是否配置
    if not config.base_url:
        return jsonify({
            'error': 'API URL not configured',
            'message': '模型API基础URL未配置，请先在模型设置中配置正确的API地址'
        }), 400

    # 获取请求数据
    data = request.get_json()
    prompt = data.get('prompt', 'Hello, respond with a short greeting.')
    system_prompt = data.get('system_prompt', 'You are a helpful assistant.')

    # 获取高级参数
    advanced_params = {}
    params_mapping = {
        'temperature': 'temperature',
        'top_p': 'top_p',
        'frequency_penalty': 'frequency_penalty',
        'presence_penalty': 'presence_penalty',
        'max_tokens': 'max_tokens',
        'stop': 'stop_sequences'
    }

    for front_param, back_param in params_mapping.items():
        if front_param in data:
            advanced_params[back_param] = data[front_param]

    logger.info(f"[SSE] 请求参数: prompt长度={len(prompt)}, 高级参数={advanced_params}")

    # 创建统一模型客户端实例
    model_client = ModelClient()

    # 创建队列用于线程间通信
    result_queue = queue.Queue()

    # 回调函数，将内容放入队列
    def handle_chunk(content):
        if content:
            try:
                # 确保内容是UTF-8字符串
                if isinstance(content, bytes):
                    content = content.decode('utf-8', errors='replace')

                # 创建SSE格式的数据 - 不再每个字符单独发送，而是整块发送
                data = json.dumps({
                    "choices": [{
                        "delta": {
                            "content": content
                        }
                    }]
                }, ensure_ascii=False)

                # 将数据放入队列
                result_queue.put(f"data: {data}\n\n")

                # 不再使用join等待，避免task_done调用过多问题
                # result_queue.join()  # 等待生成器处理完这个块
            except Exception as e:
                logger.error(f"[SSE] 处理内容错误: {str(e)}")

    # 在后台线程中运行模型
    def run_model():
        try:
            # 减少日志量，只保留必要信息
            logger.info(f"[SSE] 开始模型流式测试: 模型ID={config.id}")

            # 调用test_model_stream方法，传递高级参数
            result = model_client.test_model_stream(
                config=config,
                prompt=prompt,
                system_prompt=system_prompt,
                callback=handle_chunk,
                **advanced_params  # 传递所有高级参数
            )

            # 如果返回的是错误结果，发送错误消息
            if isinstance(result, dict) and result.get('success') is False:
                error_msg = result.get('message', '未知错误')
                logger.error(f"[SSE] 模型返回错误: {error_msg}")
                error_json = json.dumps({"error": error_msg}, ensure_ascii=False)
                result_queue.put(f"data: {error_json}\n\n")

            # 简化日志
            logger.info("[SSE] 模型流式测试完成")
        except Exception as e:
            # 发送错误信息
            logger.error(f"[SSE] 模型流式测试出错: {str(e)}")
            error_json = json.dumps({"error": str(e)}, ensure_ascii=False)
            result_queue.put(f"data: {error_json}\n\n")
        finally:
            # 发送结束标记
            result_queue.put("data: [DONE]\n\n")

    # 定义生成器函数，用于流式传输响应
    def generate():
        # 发送初始状态
        yield "data: {\"status\": \"connected\"}\n\n"
        sys.stdout.flush()  # 确保立即发送

        # 启动模型线程
        model_thread = threading.Thread(target=run_model)
        model_thread.daemon = True
        model_thread.start()

        # 持续从队列获取内容并发送，直到收到结束标记
        while True:
            try:
                # 使用较小的超时确保高响应性
                chunk = result_queue.get(timeout=0.01)  # 降低超时时间，提高响应速度

                # 发送块并立即刷新
                yield chunk
                sys.stdout.flush()  # 强制立即发送

                # 检查是否为结束标记
                if "data: [DONE]" in chunk:
                    break

                # 不再调用task_done，彻底移除这个操作
                # result_queue.task_done()

                # 非常短的延迟，只为了确保数据能被发送出去
                time.sleep(0.0001)

            except queue.Empty:
                # 发送心跳以保持连接
                yield ": keepalive\n\n"
                sys.stdout.flush()  # 确保立即发送
                time.sleep(0.01)  # 短暂等待，不要过快发送心跳
                continue
            except GeneratorExit:
                # 客户端断开连接
                logger.info("[SSE] 客户端断开连接")
                break
            except Exception as e:
                logger.error(f"[SSE] 生成器异常: {str(e)}")
                error_json = json.dumps({"error": str(e)}, ensure_ascii=False)
                yield f"data: {error_json}\n\n"
                sys.stdout.flush()
                yield "data: [DONE]\n\n"
                sys.stdout.flush()
                break

    # 创建响应对象
    response = Response(
        stream_with_context(generate()),
        mimetype='text/event-stream',
        headers={
            'Cache-Control': 'no-cache, no-transform',
            'Connection': 'keep-alive',
            'Access-Control-Allow-Origin': '*',
            'X-Accel-Buffering': 'no',
            'Content-Type': 'text/event-stream; charset=utf-8'
        }
    )

    return response

@model_bp.route('/model-configs/<int:config_id>/test-stream', methods=['OPTIONS'])
def options_sse_test_model_config(config_id):
    """处理SSE流式测试的OPTIONS请求"""
    return '', 200, {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type'
    }

@model_bp.route('/model-configs/gpustack/models', methods=['POST'])
def fetch_gpustack_models():
    """获取GPUStack模型列表"""
    import requests

    data = request.get_json()
    base_url = data.get('base_url')
    api_key = data.get('api_key')

    if not base_url or not api_key:
        return jsonify({
            'success': False,
            'message': '缺少必要参数：base_url 和 api_key'
        }), 400

    try:
        # 构建GPUStack API URL
        models_url = base_url.rstrip('/') + '/models'

        # 发送请求到GPUStack
        response = requests.get(models_url, headers={
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }, timeout=10)

        if response.status_code == 200:
            data = response.json()
            models = data.get('data', [])
            return jsonify({
                'success': True,
                'models': models,
                'message': f'成功获取到 {len(models)} 个模型'
            })
        else:
            return jsonify({
                'success': False,
                'message': f'GPUStack API错误: {response.status_code} - {response.text}'
            }), response.status_code

    except requests.exceptions.Timeout:
        return jsonify({
            'success': False,
            'message': '请求超时，请检查GPUStack服务器是否可访问'
        }), 408
    except requests.exceptions.ConnectionError:
        return jsonify({
            'success': False,
            'message': '连接失败，请检查GPUStack服务器地址和网络连接'
        }), 503
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取模型列表失败: {str(e)}'
        }), 500


@model_bp.route('/model-configs/ollama/models', methods=['POST'])
def fetch_ollama_models():
    """获取Ollama模型列表"""
    import requests

    data = request.get_json()
    base_url = data.get('base_url')

    if not base_url:
        return jsonify({
            'success': False,
            'message': '缺少必要参数：base_url'
        }), 400

    try:
        # 构建Ollama API URL
        models_url = base_url.rstrip('/') + '/api/tags'

        # 发送请求到Ollama
        response = requests.get(models_url, timeout=10)

        if response.status_code == 200:
            data = response.json()
            models = data.get('models', [])
            return jsonify({
                'success': True,
                'models': models,
                'message': f'成功获取到 {len(models)} 个模型'
            })
        else:
            return jsonify({
                'success': False,
                'message': f'Ollama API错误: {response.status_code} - {response.text}'
            }), response.status_code

    except requests.exceptions.Timeout:
        return jsonify({
            'success': False,
            'message': '请求超时，请检查Ollama服务器是否可访问'
        }), 408
    except requests.exceptions.ConnectionError:
        return jsonify({
            'success': False,
            'message': '连接失败，请检查Ollama服务器地址和网络连接'
        }), 503
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取模型列表失败: {str(e)}'
        }), 500


@model_bp.route('/model-configs/anthropic/models', methods=['POST'])
def fetch_anthropic_models():
    """获取Anthropic模型列表"""
    import requests

    data = request.get_json()
    base_url = data.get('base_url')
    api_key = data.get('api_key')

    if not base_url or not api_key:
        return jsonify({
            'success': False,
            'message': '缺少必要参数：base_url 和 api_key'
        }), 400

    try:
        # 构建Anthropic API URL
        # 根据Claude API文档，模型列表端点是 /v1/models
        if '/v1/models' in base_url:
            models_url = base_url
        elif '/v1' in base_url:
            models_url = base_url.rstrip('/') + '/models'
        else:
            models_url = base_url.rstrip('/') + '/v1/models'

        # 发送请求到Anthropic API
        response = requests.get(models_url, headers={
            'x-api-key': api_key,
            'anthropic-version': '2023-06-01',
            'Content-Type': 'application/json'
        }, timeout=10)

        if response.status_code == 200:
            data = response.json()
            models = data.get('data', [])
            return jsonify({
                'success': True,
                'models': models,
                'message': f'成功获取到 {len(models)} 个模型'
            })
        else:
            return jsonify({
                'success': False,
                'message': f'Anthropic API错误: {response.status_code} - {response.text}'
            }), response.status_code

    except requests.exceptions.Timeout:
        return jsonify({
            'success': False,
            'message': '请求超时，请检查Anthropic服务器是否可访问'
        }), 408
    except requests.exceptions.ConnectionError:
        return jsonify({
            'success': False,
            'message': '连接失败，请检查Anthropic服务器地址和网络连接'
        }), 503
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取模型列表失败: {str(e)}'
        }), 500


@model_bp.route('/model-configs/google/models', methods=['POST'])
def fetch_google_models():
    """获取Google AI模型列表"""
    import requests

    data = request.get_json()
    base_url = data.get('base_url')
    api_key = data.get('api_key')

    if not base_url or not api_key:
        return jsonify({
            'success': False,
            'message': '缺少必要参数：base_url 和 api_key'
        }), 400

    try:
        # 构建Google AI API URL
        models_url = base_url.rstrip('/') + '/models'

        # 发送请求到Google AI，API Key通过查询参数传递
        response = requests.get(models_url, params={
            'key': api_key
        }, headers={
            'Content-Type': 'application/json'
        }, timeout=10)

        if response.status_code == 200:
            data = response.json()
            models = data.get('models', [])
            return jsonify({
                'success': True,
                'models': models,
                'message': f'成功获取到 {len(models)} 个模型'
            })
        else:
            return jsonify({
                'success': False,
                'message': f'Google AI API错误: {response.status_code} - {response.text}'
            }), response.status_code

    except requests.exceptions.Timeout:
        return jsonify({
            'success': False,
            'message': '请求超时，请检查Google AI服务器是否可访问'
        }), 408
    except requests.exceptions.ConnectionError:
        return jsonify({
            'success': False,
            'message': '连接失败，请检查Google AI服务器地址和网络连接'
        }), 503
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取模型列表失败: {str(e)}'
        }), 500


@model_bp.route('/model-configs/test-connection', methods=['POST'])
def test_connection():
    """测试模型服务连接"""
    import requests

    data = request.get_json()
    base_url = data.get('base_url')
    provider = data.get('provider', '')
    api_key = data.get('api_key', '')

    if not base_url:
        return jsonify({
            'success': False,
            'message': '缺少必要参数：base_url'
        }), 400

    try:
        # 根据不同提供商构建测试URL
        if provider.lower() == 'ollama':
            # Ollama使用/api/tags端点测试连接
            test_url = base_url.rstrip('/') + '/api/tags'
        elif provider.lower() == 'gpustack':
            # GPUStack使用/models端点测试连接
            test_url = base_url.rstrip('/') + '/models'
        elif provider.lower() == 'anthropic':
            # Anthropic使用/v1/models端点测试连接
            if '/v1/models' in base_url:
                test_url = base_url
            elif '/v1' in base_url:
                test_url = base_url.rstrip('/') + '/models'
            else:
                test_url = base_url.rstrip('/') + '/v1/models'
        elif provider.lower() == 'google':
            # Google AI使用/models端点测试连接
            test_url = base_url.rstrip('/') + '/models'
        elif provider.lower() == 'xai':
            # X.ai使用/v1/models端点测试连接
            if '/v1/models' in base_url:
                test_url = base_url
            elif '/v1' in base_url:
                test_url = base_url.rstrip('/') + '/models'
            else:
                test_url = base_url.rstrip('/') + '/v1/models'
        else:
            # 其他提供商直接测试基础URL
            test_url = base_url.rstrip('/')

        # 准备请求头
        headers = {}

        # 为需要API密钥的提供商添加认证头
        if provider.lower() in ['xai', 'openai', 'anthropic', 'deepseek'] and api_key:
            if provider.lower() == 'anthropic':
                headers['x-api-key'] = api_key
                headers['anthropic-version'] = '2023-06-01'
            else:
                headers['Authorization'] = f'Bearer {api_key}'
        elif provider.lower() == 'google' and api_key:
            # Google AI使用查询参数传递API密钥
            test_url += f'?key={api_key}'

        # 发送HEAD请求测试连接
        response = requests.head(test_url, headers=headers, timeout=10)

        if response.status_code in [200, 404, 405]:  # 404和405也表示服务可达
            return jsonify({
                'success': True,
                'message': '连接测试成功'
            })
        else:
            return jsonify({
                'success': False,
                'message': f'连接测试失败: HTTP {response.status_code}'
            }), response.status_code

    except requests.exceptions.Timeout:
        return jsonify({
            'success': False,
            'message': '连接超时，请检查服务器是否可访问'
        }), 408
    except requests.exceptions.ConnectionError:
        return jsonify({
            'success': False,
            'message': '连接失败，请检查服务器地址和网络连接'
        }), 503
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'连接测试失败: {str(e)}'
        }), 500


@model_bp.route('/model-configs/<int:config_id>/test', methods=['POST'])
def test_model_config(config_id):
    """测试模型配置是否能正常连接"""
    from app.services.conversation.model_client import ModelClient

    config = ModelConfig.query.get(config_id)
    if not config:
        return jsonify({'error': 'Model configuration not found'}), 404

    # 检查URL是否配置
    if not config.base_url:
        return jsonify({
            'error': 'API URL not configured',
            'message': '模型API基础URL未配置，请先在模型设置中配置正确的API地址'
        }), 400

    data = request.get_json()
    use_stream = data.get('stream', False)
    prompt = data.get('prompt', 'Hello, respond with a short greeting only.')
    system_prompt = data.get('system_prompt', 'You are a helpful assistant.')

    # 获取高级参数
    advanced_params = {}
    params_mapping = {
        'temperature': 'temperature',
        'top_p': 'top_p',
        'frequency_penalty': 'frequency_penalty',
        'presence_penalty': 'presence_penalty',
        'max_tokens': 'max_tokens',
        'stop': 'stop_sequences'
    }

    for front_param, back_param in params_mapping.items():
        if front_param in data:
            advanced_params[back_param] = data[front_param]

    logger.info(f"[API] 测试请求参数: prompt长度={len(prompt)}, 高级参数={advanced_params}")

    # 创建统一模型客户端实例
    model_client = ModelClient()

    # 使用统一模型客户端的test_model方法进行测试，传递高级参数
    result = model_client.test_model(
        config=config,
        prompt=prompt,
        use_stream=use_stream,
        system_prompt=system_prompt,
        **advanced_params  # 传递所有高级参数
    )

    logger.debug(f"DEBUG - test_model_config - Raw result: {result}")

    # 构建响应对象
    response_obj = {
        'success': result.get('success', False),
        'message': result.get('message', ''),
    }

    # 如果有response字段，则包含它
    if 'response' in result:
        response_obj['response'] = result['response']
    # 否则，尝试从message提取response
    elif result.get('success', False) and '测试成功:' in result.get('message', ''):
        message = result.get('message', '')
        response_text = message.split('测试成功:', 1)[1].strip()
        # 移除末尾的省略号
        if response_text.endswith('...'):
            response_text = response_text[:-3]
        response_obj['response'] = response_text

    return jsonify(response_obj)


@model_bp.route('/model-configs/xai/models', methods=['POST'])
def fetch_xai_models():
    """获取X.ai模型列表"""
    import requests

    data = request.get_json()
    base_url = data.get('base_url')
    api_key = data.get('api_key')

    if not base_url or not api_key:
        return jsonify({
            'success': False,
            'message': '缺少必要参数：base_url 或 api_key'
        }), 400

    try:
        # 构建X.ai API URL
        # 根据X.ai API文档，模型列表端点是 /v1/models
        if '/v1/models' in base_url:
            models_url = base_url
        elif '/v1' in base_url:
            models_url = base_url.rstrip('/') + '/models'
        else:
            models_url = base_url.rstrip('/') + '/v1/models'

        # 发送请求到X.ai API
        response = requests.get(models_url, headers={
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }, timeout=10)

        if response.status_code == 200:
            data = response.json()
            models = data.get('data', [])
            return jsonify({
                'success': True,
                'models': models,
                'message': f'成功获取到 {len(models)} 个模型'
            })
        else:
            return jsonify({
                'success': False,
                'message': f'X.ai API错误: {response.status_code} - {response.text}'
            }), response.status_code

    except requests.exceptions.Timeout:
        return jsonify({
            'success': False,
            'message': '请求超时，请检查网络连接或API地址'
        }), 408
    except requests.exceptions.ConnectionError:
        return jsonify({
            'success': False,
            'message': '连接失败，请检查API地址是否正确'
        }), 503
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'请求失败: {str(e)}'
        }), 500