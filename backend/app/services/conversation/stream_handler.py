"""
流式处理模块

提供处理流式响应和SSE事件流的功能

函数与关键变量说明:
---------------------------------------

SSE相关函数:
* create_sse_response - 创建SSE响应
  - generator_function: 生成器函数

* queue_to_sse - 将队列内容转换为SSE事件流
  - result_queue: 结果队列

* wrap_stream_callback - 包装流式回调函数
  - result_queue: 结果队列

流式响应处理:
* handle_streaming_response - 处理流式响应
  - response: 响应对象
  - callback: 回调函数

* call_llm_with_tool_results - 在工具调用执行后再次调用LLM
  - original_messages: 原始消息历史
  - tool_calls: 工具调用列表
  - tool_results: 工具调用结果列表
  - api_config: API配置信息
  - callback: 回调函数
"""
import json
import logging
import queue
import re
import traceback
import uuid
import requests
from typing import Dict, Any, Callable, Generator, List

from flask import Response, stream_with_context
from config import DEBUG_LLM_RESPONSE
from app.services.conversation.tool_handler import execute_tool_call, parse_tool_calls
from app.services.conversation.message_formater import format_tool_call, format_tool_result_as_role, serialize_message
from app.services.conversation.model_client import ModelClient
# 导入连接管理器
from app.services.conversation.connection_manager import connection_manager

logger = logging.getLogger(__name__)

# 自定义异常类，用于主动中断流式处理
class StreamCancelledException(Exception):
    """流式处理被取消异常"""
    def __init__(self, request_id: str, agent_id: str = None):
        self.request_id = request_id
        self.agent_id = agent_id
        super().__init__(f"流式处理被取消: {request_id}")

# 添加全局变量来跟踪流式任务
_active_streaming_tasks = {}
# 添加全局变量来跟踪智能体流式任务
_agent_streaming_tasks = {}

def create_sse_response(generator_function: Callable) -> Response:
    """创建SSE响应"""
    return Response(
        stream_with_context(generator_function()),
        content_type='text/event-stream',
        headers={
            'Cache-Control': 'no-cache',
            'X-Accel-Buffering': 'no'
        }
    )

def queue_to_sse(result_queue: queue.Queue) -> Generator[str, None, None]:
    """将队列内容转换为SSE事件流"""
    # 导入格式化函数
    from app.services.conversation.message_formater import format_agent_cancel_done, serialize_message
    from app.models import Agent, Role

    while True:
        message = result_queue.get()
        if message is None:  # 结束信号
            # 使用空行或结束事件表示传输结束
            yield "data: \n\n"
            break

        # 检查是否是取消信号
        if isinstance(message, dict) and message.get('type') == 'cancel':
            logger.info(f"收到取消信号: {message}")

            # 如果包含agent_id，说明是特定智能体的取消信号
            agent_id = message.get('agent_id')
            if agent_id:
                try:
                    # 获取智能体信息
                    agent = Agent.query.get(agent_id)
                    if agent:
                        agent_role = Role.query.get(agent.role_id) if hasattr(agent, 'role_id') and agent.role_id else None
                        role_name = agent_role.name if agent_role else "智能助手"

                        # 生成取消消息
                        cancel_content = f"智能体响应被用户取消: {agent.name}({role_name})"

                        # 格式化取消完成消息
                        cancel_done_msg = format_agent_cancel_done(
                            agent_id=str(agent_id),
                            agent_name=agent.name,
                            role_name=role_name,
                            timestamp=None,
                            response_order=1,
                            cancel_content=cancel_content
                        )

                        # 序列化并发送
                        cancel_done_str = serialize_message(cancel_done_msg)
                        yield f"data: {cancel_done_str}\n\n"

                        logger.info(f"已发送智能体取消完成消息: {agent_id}")
                except Exception as e:
                    logger.error(f"处理智能体取消信号出错: {str(e)}")
                    error_msg = f"错误: 处理智能体取消信号失败: {str(e)}"
                    yield f"data: {error_msg}\n\n"

            # 无论是否有agent_id，都结束流
            yield "data: \n\n"
            break

        # 检查是否是智能体取消信号
        elif isinstance(message, dict) and message.get('type') == 'agent_cancel':
            logger.info(f"收到智能体取消信号: {message}")

            # 获取智能体ID
            agent_id = message.get('agent_id')
            if agent_id:
                try:
                    # 获取智能体信息
                    agent = Agent.query.get(agent_id)
                    if agent:
                        agent_role = Role.query.get(agent.role_id) if hasattr(agent, 'role_id') and agent.role_id else None
                        role_name = agent_role.name if agent_role else "智能助手"

                        # 生成取消消息
                        cancel_content = f"智能体响应被用户取消: {agent.name}({role_name})"

                        # 格式化取消完成消息
                        cancel_done_msg = format_agent_cancel_done(
                            agent_id=str(agent_id),
                            agent_name=agent.name,
                            role_name=role_name,
                            timestamp=None,
                            response_order=1,
                            cancel_content=cancel_content
                        )

                        # 序列化并发送
                        cancel_done_str = serialize_message(cancel_done_msg)
                        yield f"data: {cancel_done_str}\n\n"

                        logger.info(f"已发送智能体取消完成消息: {agent_id}")
                except Exception as e:
                    logger.error(f"处理智能体取消信号出错: {str(e)}")
                    error_msg = f"错误: 处理智能体取消信号失败: {str(e)}"
                    yield f"data: {error_msg}\n\n"

                # 不结束流，继续处理其他消息
                continue

        # 如果是字典，序列化为JSON
        if isinstance(message, dict):
            try:
                message = json.dumps(message, ensure_ascii=False)
                yield f"data: {message}\n\n"
            except Exception as e:
                error_msg = f"错误: 无法序列化消息为JSON: {str(e)}"
                yield f"data: {error_msg}\n\n"
        else:
            # 否则直接发送字符串
            yield f"data: {message}\n\n"

def wrap_stream_callback(result_queue: queue.Queue) -> Callable:
    """包装流式回调函数"""
    def callback(content):
        result_queue.put(content)
    return callback

def register_streaming_task(task_id: int, conversation_id: int, result_queue: queue.Queue, agent_id: str = None) -> None:
    """
    注册流式任务以便后续管理

    Args:
        task_id: 行动任务ID
        conversation_id: 会话ID
        result_queue: 结果队列
        agent_id: 智能体ID，如果提供则同时注册智能体流式任务
    """
    task_key = f"{task_id}:{conversation_id}"
    _active_streaming_tasks[task_key] = result_queue

    # 如果提供了智能体ID，同时注册智能体流式任务
    if agent_id:
        agent_key = f"{task_id}:{conversation_id}:{agent_id}"
        _agent_streaming_tasks[agent_key] = result_queue
        logger.info(f"已注册智能体流式任务: {agent_key}")

    logger.info(f"已注册流式任务: {task_key}")

def cancel_streaming_task(task_id: int, conversation_id: int, agent_id: str = None) -> bool:
    """
    取消正在进行的流式任务

    Args:
        task_id: 行动任务ID
        conversation_id: 会话ID
        agent_id: 智能体ID，如果提供则只取消该智能体的流式任务

    Returns:
        bool: 是否成功取消任务
    """
    # 导入格式化函数和取消请求函数
    from app.services.conversation.message_formater import format_agent_cancel_done, serialize_message
    from app.services.conversation.model_client import cancel_request

    # 记录开始取消任务
    logger.info(f"开始取消流式任务: task_id={task_id}, conversation_id={conversation_id}, agent_id={agent_id}")

    # 首先尝试取消底层HTTP请求 - 这是最重要的一步，确保LLM请求被终止
    cancel_success = cancel_request(task_id, conversation_id, agent_id)
    if cancel_success:
        logger.info(f"成功取消底层HTTP请求: task_id={task_id}, conversation_id={conversation_id}, agent_id={agent_id}")

    # 尝试调用外部平台的停止API（如果是外部角色）
    external_stop_success = False
    if agent_id:
        try:
            # 检查是否是外部角色，如果是则调用外部平台停止API
            from app.services.conversation.external_model_client import external_model_client
            external_stop_success = external_model_client.stop_external_streaming(task_id, conversation_id, agent_id)
            if external_stop_success:
                logger.info(f"成功调用外部平台停止API: task_id={task_id}, conversation_id={conversation_id}, agent_id={agent_id}")
        except Exception as e:
            logger.warning(f"调用外部平台停止API失败: {str(e)}")
            # 不影响整体流程，继续执行其他停止逻辑

    # 标记是否成功取消了任何任务
    any_success = cancel_success or external_stop_success

    # 如果提供了智能体ID，尝试取消特定智能体的流式任务
    if agent_id:
        # 确保智能体ID是字符串类型
        agent_id = str(agent_id)

        # 构建智能体键
        agent_key = f"{task_id}:{conversation_id}:{agent_id}"

        # 记录调试信息
        logger.info(f"尝试取消智能体流式任务: {agent_key}")
        logger.info(f"当前活动的智能体流式任务: {list(_agent_streaming_tasks.keys())}")

        # 查找所有匹配的智能体任务键
        matching_keys = []

        # 精确匹配
        if agent_key in _agent_streaming_tasks:
            matching_keys.append(agent_key)
        else:
            # 模糊匹配 - 处理ID格式不一致的情况
            for key in _agent_streaming_tasks.keys():
                parts = key.split(':')
                if len(parts) == 3 and parts[0] == str(task_id) and parts[1] == str(conversation_id):
                    # 检查第三部分是否与智能体ID匹配（忽略类型）
                    if parts[2] == agent_id or parts[2] == str(agent_id):
                        matching_keys.append(key)

        # 处理所有匹配的智能体任务
        for matching_key in matching_keys:
            try:
                # 获取该智能体任务的队列
                queue_obj = _agent_streaming_tasks[matching_key]

                # 发送取消信号到队列
                queue_obj.put({
                    "type": "cancel",
                    "message": "用户取消了智能体流式输出",
                    "agent_id": agent_id
                })

                # 从智能体活动任务中移除
                del _agent_streaming_tasks[matching_key]

                logger.info(f"成功取消匹配的智能体流式任务: {matching_key}")
                any_success = True
            except Exception as e:
                logger.error(f"取消匹配的智能体流式任务出错: {str(e)}")
                # 即使出现异常，也认为取消成功，避免前端卡住
                any_success = True

        # 如果没有找到匹配的智能体任务，尝试通过常规任务取消
        if not matching_keys:
            logger.info(f"未找到智能体流式任务: {agent_key}，尝试通过常规任务取消")

            # 尝试从常规任务中查找
            task_key = f"{task_id}:{conversation_id}"
            if task_key in _active_streaming_tasks:
                try:
                    # 获取该任务的队列
                    queue_obj = _active_streaming_tasks[task_key]

                    # 发送智能体取消信号到队列
                    queue_obj.put({
                        "type": "agent_cancel",
                        "message": "用户取消了智能体流式输出",
                        "agent_id": agent_id
                    })

                    logger.info(f"通过常规任务取消智能体流式任务: {agent_id}")
                    any_success = True
                except Exception as e:
                    logger.error(f"通过常规任务取消智能体流式任务出错: {str(e)}")
                    # 即使出现异常，也认为取消成功，避免前端卡住
                    any_success = True
    else:
        # 如果没有提供智能体ID，取消整个会话的流式任务
        task_key = f"{task_id}:{conversation_id}"
        if task_key in _active_streaming_tasks:
            try:
                # 获取该任务的队列
                queue_obj = _active_streaming_tasks[task_key]

                # 发送取消信号到队列
                queue_obj.put({
                    "type": "cancel",
                    "message": "用户取消了流式输出"
                })

                # 从活动任务中移除
                del _active_streaming_tasks[task_key]

                # 同时清理所有相关的智能体流式任务
                agent_keys = [k for k in _agent_streaming_tasks.keys() if k.startswith(f"{task_id}:{conversation_id}:")]
                for agent_key in agent_keys:
                    if agent_key in _agent_streaming_tasks:
                        del _agent_streaming_tasks[agent_key]

                logger.info(f"成功取消流式任务: {task_key}")
                any_success = True
            except Exception as e:
                logger.error(f"取消流式任务出错: {str(e)}")
                # 即使出现异常，也认为取消成功，避免前端卡住
                any_success = True
        else:
            logger.info(f"未找到流式任务: {task_key}")
            # 即使未找到任务，也返回成功，避免前端卡住
            any_success = True

    # 尝试取消自动讨论任务
    try:
        # 导入自动讨论模块
        from app.services.conversation.auto_conversation import stop_auto_discussion

        # 如果提供了agent_id，说明是中断特定智能体
        # 在自主任务中，不需要特殊处理，让_process_single_agent_response自然处理StreamCancelledException
        # 它会发送agentCancelDone消息并让自主任务循环继续
        if agent_id:
            logger.info(f"中断自主任务中的智能体: task_id={task_id}, conversation_id={conversation_id}, agent_id={agent_id}")
            logger.info("自主任务中的智能体中断将由_process_single_agent_response自然处理")
            any_success = True
        else:
            # 如果没有提供agent_id，停止整个自主任务
            logger.info(f"停止整个自主任务: task_id={task_id}, conversation_id={conversation_id}")
            auto_discussion_stopped = stop_auto_discussion(task_id, conversation_id)
            if auto_discussion_stopped:
                logger.info(f"成功停止自动讨论任务: task_id={task_id}, conversation_id={conversation_id}")
                any_success = True
    except Exception as e:
        logger.error(f"处理自动讨论任务出错: {str(e)}")
        # 即使出现异常，也认为取消成功，避免前端卡住
        any_success = True

    # 返回是否成功取消任何任务 - 始终返回成功，确保前端不会卡住
    return True

def call_llm_with_tool_results(original_messages: List[Dict[str, Any]],
                         tool_calls: List[Dict[str, Any]],
                         tool_results: List[Dict[str, Any]],
                         api_config: Dict[str, Any],
                         callback: Callable):
    """
    在工具调用执行后再次调用LLM

    Args:
        original_messages: 原始消息历史
        tool_calls: 工具调用列表
        tool_results: 工具调用结果列表
        api_config: API配置信息，包含api_url, api_key, model, agent_info等
        callback: 回调函数

    Returns:
        str: LLM的最终回复
    """
    if DEBUG_LLM_RESPONSE:
        logger.debug("\n" + "="*40)
        logger.debug("[工具调用后再次调用LLM] 开始处理")
        logger.debug("-"*40)

    try:
        # 1. 构建新的消息历史，包含原始消息、工具调用和工具调用结果
        updated_messages = original_messages.copy()

        # 2. 为每个工具调用和结果添加消息（使用OpenAI标准的格式）
        # 创建一个新的assistant消息，包含工具调用
        assistant_message = {
            "role": "assistant",
            "content": "",  # 空字符串而不是None
            "tool_calls": []
        }

        # 添加工具调用
        for tool_call in tool_calls:
            # 确保每个工具调用都有唯一的ID
            tool_call_id = tool_call.get("id")
            if not tool_call_id:
                tool_call_id = str(uuid.uuid4())
                tool_call["id"] = tool_call_id  # 更新原始工具调用的ID

            assistant_message["tool_calls"].append({
                "id": tool_call_id,
                "type": "function",
                "function": {
                    "name": tool_call["function"]["name"],
                    "arguments": tool_call["function"]["arguments"]
                }
            })

        # 添加到消息历史
        updated_messages.append(assistant_message)

        if DEBUG_LLM_RESPONSE:
            logger.debug(f"[工具调用后再次调用LLM] 创建了新的assistant消息，包含 {len(assistant_message['tool_calls'])} 个工具调用")

        # 添加工具调用结果
        for i, tool_call in enumerate(tool_calls):
            # 获取工具调用ID - 使用之前更新过的ID
            tool_call_id = tool_call.get("id")

            # 获取工具调用结果
            if i < len(tool_results):
                tool_result = tool_results[i]
                result_content = tool_result.get("result", "")

                # 添加标准OpenAI格式的工具调用结果消息
                tool_result_message = {
                    "role": "tool",
                    "tool_call_id": tool_call_id,
                    "content": result_content
                }
                updated_messages.append(tool_result_message)

                if DEBUG_LLM_RESPONSE:
                    logger.debug(f"[工具调用后再次调用LLM] 添加工具结果消息: {tool_call['function']['name']}, ID: {tool_call_id}")

        if DEBUG_LLM_RESPONSE:
            logger.debug(f"[工具调用后再次调用LLM] 更新后的消息历史包含 {len(updated_messages)} 条消息")
            logger.debug(f"[工具调用后再次调用LLM] 添加了 {len(tool_calls)} 个工具调用和 {len(tool_results)} 个工具结果")

            # 打印完整的消息历史，用于调试
            logger.debug("[工具调用后再次调用LLM] 完整消息历史:")
            for i, msg in enumerate(updated_messages):
                if msg.get("role") == "tool":
                    logger.debug(f"  [{i+1}] {msg.get('role')}: tool_call_id={msg.get('tool_call_id')}, content={msg.get('content')[:50]}...")
                elif msg.get("role") == "assistant" and msg.get("tool_calls"):
                    tool_calls_info = [f"{tc.get('function', {}).get('name')}({tc.get('id')})" for tc in msg.get("tool_calls", [])]
                    logger.debug(f"  [{i+1}] {msg.get('role')}: tool_calls={tool_calls_info}")
                else:
                    content = msg.get("content", "")
                    if content and len(content) > 50:
                        content = content[:50] + "..."
                    logger.debug(f"  [{i+1}] {msg.get('role')}: {content}")

        # 不再需要向前端发送HTML注释通知
        # 前端可以通过消息类型识别工具调用和结果处理的状态

        # 3. 再次调用LLM，传入更新后的消息历史
        model_client = ModelClient()

        # 准备API配置
        api_url = api_config.get("api_url")
        api_key = api_config.get("api_key")
        model = api_config.get("model")
        agent_info = api_config.get("agent_info")

        # 其他参数
        kwargs = {k: v for k, v in api_config.items()
                 if k not in ["api_url", "api_key", "model", "agent_info"]}

        if DEBUG_LLM_RESPONSE:
            logger.debug(f"[工具调用后再次调用LLM] 使用模型: {model}")
            logger.debug(f"[工具调用后再次调用LLM] API URL: {api_url}")

            # 打印完整的消息历史JSON，用于调试
            logger.debug("[工具调用后再次调用LLM] 完整消息历史JSON:")
            logger.debug(json.dumps(updated_messages, ensure_ascii=False, indent=2))

        # 4. 发送请求并获取响应 - 确保使用流式响应
        final_response = model_client.send_request(
            api_url=api_url,
            api_key=api_key,
            messages=updated_messages,
            model=model,
            is_stream=True,  # 使用流式响应
            callback=callback,  # 使用相同的回调函数
            agent_info=agent_info,
            **kwargs  # 这里已经包含了task_id和conversation_id
        )

        if DEBUG_LLM_RESPONSE:
            logger.debug(f"[工具调用后再次调用LLM] 完成处理，响应长度: {len(final_response) if final_response else 0}")
            logger.debug("="*40 + "\n")

        return final_response

    except Exception as e:
        error_msg = f"工具调用后再次调用LLM时出错: {str(e)}\n{traceback.format_exc()}"
        logger.error(error_msg)

        # 发送错误消息给客户端
        if callback:
            callback(f"\n[错误] {error_msg}")

        return f"Error: {error_msg}"

def handle_streaming_response_with_adapter(adapter, response, callback, api_config=None):
    """使用适配器处理流式响应"""
    if DEBUG_LLM_RESPONSE:
        logger.debug(f"[适配器流式响应] 开始处理{adapter.platform_name}流式响应...")

    full_content = ""

    try:
        for line in response.iter_lines(decode_unicode=True):
            if line:
                # 使用适配器解析流式响应块
                content, meta = adapter.parse_streaming_chunk(line)

                if content:
                    full_content += content
                    if callback:
                        callback(content)

                if meta:
                    # 补充智能体信息到元数据
                    agent_info = api_config.get('agent_info', {}) if api_config else {}
                    if 'agentId' in meta and meta['agentId'] is None:
                        meta['agentId'] = str(agent_info.get('id', 'unknown'))
                        meta['agentName'] = agent_info.get('name', '外部智能体')
                        meta['roleName'] = agent_info.get('role_name', '外部角色')

                    if callback:
                        try:
                            callback(None, meta)
                        except TypeError:
                            # 如果回调函数只接受一个参数
                            pass

        if DEBUG_LLM_RESPONSE:
            logger.debug(f"[适配器流式响应] {adapter.platform_name}流式响应处理完成，内容长度: {len(full_content)}")

        return full_content

    except Exception as e:
        error_msg = f"处理{adapter.platform_name}流式响应失败: {str(e)}"
        logger.error(f"[适配器流式响应] 错误: {error_msg}")

        if callback:
            try:
                from app.services.conversation.message_formater import format_agent_error_done

                agent_info = api_config.get('agent_info', {}) if api_config else {}
                agent_id = str(agent_info.get('id', 'unknown'))
                agent_name = agent_info.get('name', '外部智能体')
                role_name = agent_info.get('role_name', '外部角色')

                formatted_error = format_agent_error_done(
                    agent_id=agent_id,
                    agent_name=agent_name,
                    role_name=role_name,
                    error_content=error_msg
                )

                try:
                    callback(None, formatted_error["meta"])
                except TypeError:
                    callback(f"\n[错误] {error_msg}")
            except Exception as format_error:
                logger.error(f"发送错误回调失败: {format_error}")
                try:
                    callback(f"\n[错误] {error_msg}")
                except:
                    pass

        return f"Error: {error_msg}"

def handle_streaming_response(response, callback, original_messages=None, api_config=None):
    """处理流式响应"""
    # 初始化状态变量
    full_content = ""
    buffer = ""
    has_reasoning = False  # 用于跟踪是否有未关闭的reasoning标签


    # 用于跟踪和累积OpenAI格式的工具调用
    openai_tool_calls = []
    current_tool_call = {}
    openai_tool_call_collecting = False

    # 用于检测取消信号
    is_cancelled = False
    cancelled_agent_id = None

    if DEBUG_LLM_RESPONSE:
        logger.debug("\n" + "="*40)
        logger.debug("[LLM流式响应] 开始处理LLM流式响应")
        logger.debug("-"*40)

    # 创建一个检查取消信号的函数
    def check_for_cancel_signal():
        """检查是否有取消信号 - 使用连接管理器检查"""
        nonlocal is_cancelled, cancelled_agent_id

        # 如果已经被取消，直接返回True
        if is_cancelled:
            return True

        # 首先检查连接管理器中的取消状态和中断标志
        # 尝试从api_config中获取请求ID信息
        if api_config:
            task_id = api_config.get('task_id')
            conversation_id = api_config.get('conversation_id')
            agent_info = api_config.get('agent_info')
            agent_id = agent_info.get('id') if agent_info else None

            if task_id and conversation_id:
                # 生成请求ID
                if agent_id:
                    request_id = f"{task_id}:{conversation_id}:{agent_id}"
                else:
                    request_id = f"{task_id}:{conversation_id}"

                # 检查连接管理器中的取消状态
                if connection_manager.is_cancelled(request_id):
                    is_cancelled = True
                    cancelled_agent_id = agent_id
                    # 设置短超时以快速响应取消
                    set_socket_timeout(0.1)
                    logger.info(f"[LLM流式响应] 连接管理器检测到取消状态，主动抛出异常: {request_id}")
                    raise StreamCancelledException(request_id, agent_id)

                # 检查线程中断标志
                if connection_manager.should_interrupt(request_id):
                    is_cancelled = True
                    cancelled_agent_id = agent_id
                    # 设置短超时以快速响应取消
                    set_socket_timeout(0.1)
                    logger.info(f"[LLM流式响应] 连接管理器检测到线程中断标志，主动抛出异常: {request_id}")
                    raise StreamCancelledException(request_id, agent_id)

        # 如果回调函数有result_queue属性，检查队列中是否有取消信号
        if hasattr(callback, 'result_queue') and callback.result_queue:
            try:
                # 非阻塞方式获取队列中的消息
                message = callback.result_queue.get_nowait()

                # 检查是否是取消信号
                if isinstance(message, dict) and message.get('type') in ['cancel', 'agent_cancel']:
                    is_cancelled = True
                    cancelled_agent_id = message.get('agent_id')

                    logger.info(f"[LLM流式响应] 队列检测到取消信号，主动抛出异常: {message}")

                    # 发送取消完成消息
                    if callback and cancelled_agent_id:
                        try:
                            from app.services.conversation.message_formater import format_agent_cancel_done
                            from app.models import Agent, Role

                            # 获取智能体信息
                            agent = Agent.query.get(cancelled_agent_id)
                            if agent:
                                agent_role = Role.query.get(agent.role_id) if hasattr(agent, 'role_id') and agent.role_id else None
                                role_name = agent_role.name if agent_role else "智能助手"

                                # 格式化取消完成消息
                                cancel_done_msg = format_agent_cancel_done(
                                    agent_id=str(cancelled_agent_id),
                                    agent_name=agent.name,
                                    role_name=role_name,
                                    timestamp=None,
                                    response_order=1,
                                    cancel_content=f"智能体响应被用户取消: {agent.name}({role_name})"
                                )

                                # 发送取消完成消息
                                try:
                                    callback(None, cancel_done_msg["meta"])
                                except TypeError:
                                    # 如果回调函数只接受一个参数
                                    callback(f"\n[系统] 智能体响应已被用户取消")
                        except Exception as e:
                            logger.error(f"[LLM流式响应] 发送取消完成消息失败: {str(e)}")

                    # 主动抛出异常，立即中断流式处理
                    raise StreamCancelledException(
                        request_id=f"{api_config.get('task_id', 'unknown')}:{api_config.get('conversation_id', 'unknown')}:{cancelled_agent_id}" if api_config else "unknown",
                        agent_id=cancelled_agent_id
                    )
                else:
                    # 如果不是取消信号，放回队列
                    callback.result_queue.put(message)
            except queue.Empty:
                # 队列为空，没有取消信号
                pass
        return False

    # 处理流式响应 - 仅收集内容，不在流中执行工具调用
    try:
        # 使用带超时的迭代器来避免无限阻塞
        import socket

        # 动态设置socket超时 - 只在需要快速响应取消时设置短超时
        def set_socket_timeout(timeout_seconds):
            """动态设置socket超时"""
            if hasattr(response, 'raw') and hasattr(response.raw, '_connection') and hasattr(response.raw._connection, 'sock'):
                try:
                    response.raw._connection.sock.settimeout(timeout_seconds)
                    logger.debug(f"[LLM流式响应] 已设置socket超时为{timeout_seconds}秒")
                    return True
                except Exception as e:
                    logger.debug(f"[LLM流式响应] 设置socket超时失败: {str(e)}")
                    return False
            return False

        # 初始设置较长的超时时间，避免正常响应被中断
        set_socket_timeout(60)  # 60秒超时，适应正常的模型响应时间

        # 添加一个计数器来定期检查取消状态
        line_count = 0

        for line in response.iter_lines():
            # 增加行计数
            line_count += 1

            # 每处理一行数据，检查是否有取消信号
            if check_for_cancel_signal():
                logger.info("[LLM流式响应] 收到取消信号，中断流式处理")
                break

            if not line:
                # 即使是空行也要检查取消状态
                if check_for_cancel_signal():
                    logger.info("[LLM流式响应] 在空行处检测到取消信号")
                    break
                # 每10个空行检查一次取消状态（避免过于频繁）
                if line_count % 10 == 0:
                    if check_for_cancel_signal():
                        logger.info("[LLM流式响应] 在空行批次处检测到取消信号")
                        break
                continue

            # 解析SSE格式
            line_text = line.decode('utf-8')
            # 打印原始SSE行
            if DEBUG_LLM_RESPONSE:
                logger.debug(f"[LLM原始输出] {line_text}")

            if line_text.startswith('data: '):
                content = line_text[6:]  # 移除'data: '前缀

                # 处理[DONE]消息
                if content.strip() == '[DONE]':
                    if DEBUG_LLM_RESPONSE:
                        logger.debug("[LLM流式响应] 收到结束标志 [DONE]")

                    # 消息累计到buffer中，是之前的流式输出中检测工具调用的，现在已经改成输出完后再执行工具，所以这里不应发送
                    if buffer:
                        #callback(buffer)
                        logger.debug(f"[LLM流式响应] BUFFER中的最后一块内容（不发送）: {buffer}")
                    continue

                # 解析JSON
                try:
                    chunk = json.loads(content)
                    # 打印解析后的JSON内容
                    if DEBUG_LLM_RESPONSE:
                        logger.debug(f"[LLM解析内容] {json.dumps(chunk, ensure_ascii=False)}")

                    # 在处理每个chunk前检查取消状态
                    if check_for_cancel_signal():
                        logger.info("[LLM流式响应] 在处理chunk时检测到取消信号")
                        break

                    # 检测OpenAI格式的工具调用开始或继续
                    if chunk.get('choices') and chunk['choices'][0].get('delta') and chunk['choices'][0]['delta'].get('tool_calls') is not None:
                        delta_tool_calls = chunk['choices'][0]['delta']['tool_calls']

                        if DEBUG_LLM_RESPONSE:
                            logger.debug(f"[LLM流式响应] 检测到有效的tool_calls: {len(delta_tool_calls)}个")

                        for delta_tool_call in delta_tool_calls:
                            tool_call_index = delta_tool_call.get('index', 0)

                            # 确保有足够的工具调用槽位
                            while len(openai_tool_calls) <= tool_call_index:
                                openai_tool_calls.append({
                                    'id': '',
                                    'type': 'function',
                                    'function': {'name': '', 'arguments': ''}
                                })

                            # 更新id和type
                            if 'id' in delta_tool_call:
                                openai_tool_calls[tool_call_index]['id'] = delta_tool_call['id']
                            if 'type' in delta_tool_call:
                                openai_tool_calls[tool_call_index]['type'] = delta_tool_call['type']

                            # 更新函数信息
                            if 'function' in delta_tool_call:
                                if 'name' in delta_tool_call['function']:
                                    openai_tool_calls[tool_call_index]['function']['name'] = delta_tool_call['function']['name']
                                if 'arguments' in delta_tool_call['function']:
                                    if delta_tool_call['function']['arguments'] is not None:
                                        openai_tool_calls[tool_call_index]['function']['arguments'] += delta_tool_call['function']['arguments']

                        openai_tool_call_collecting = True

                    # 检查是否收到工具调用完成的信号
                    elif chunk.get('choices') and chunk['choices'][0].get('finish_reason') == 'tool_calls':
                        if DEBUG_LLM_RESPONSE:
                            logger.debug(f"[LLM流式响应] 工具调用完成信号: {len(openai_tool_calls)} 个工具调用")

                        openai_tool_call_collecting = False

                    # 检查delta的内容，收集文本内容
                    elif chunk.get('choices') and chunk['choices'][0].get('delta', {}).get('content'):
                        content_piece = chunk['choices'][0]['delta']['content']
                        # 检查是否是reasoning的结束（content有内容，但没有reasoning_content）
                        if has_reasoning and not chunk['choices'][0].get('delta', {}).get('reasoning_content'):
                            if DEBUG_LLM_RESPONSE:
                                logger.debug("[LLM流式响应] 检测到reasoning可能结束，添加</thinking>标签")

                            # 添加结束标签
                            thinking_end_tag = "\n</thinking>\n"
                            # 更新buffer和full_content
                            buffer += thinking_end_tag
                            full_content += thinking_end_tag
                            # 发送结束标签
                            callback(thinking_end_tag)
                            has_reasoning = False

                            # 将content_piece添加到buffer和full_content
                            buffer += content_piece
                            full_content += content_piece
                            # 使用回调发送内容块
                            callback(content_piece)
                        else:
                            # 将content_piece添加到buffer和full_content
                            buffer += content_piece
                            full_content += content_piece
                            # 使用回调发送内容块
                            callback(content_piece)
                    # 检查是否有reasoning_content字段（Qwen3模型特有）
                    elif chunk.get('choices') and chunk['choices'][0].get('delta', {}).get('reasoning_content'):
                        # 从Aliyun Qwen3模型响应中提取reasoning_content
                        reasoning_content = chunk['choices'][0]['delta']['reasoning_content']
                        content_value = chunk['choices'][0].get('delta', {}).get('content')

                        if DEBUG_LLM_RESPONSE:
                            logger.debug(f"[LLM流式响应] 检测到Qwen3 reasoning_content: '{reasoning_content}' (长度: {len(reasoning_content)})")
                            logger.debug(f"[LLM流式响应] content值: {content_value}")
                            logger.debug(f"[LLM流式响应] has_reasoning状态: {has_reasoning}")

                        # 检查是否是reasoning的开始（content为null，reasoning_content不为null）
                        if not has_reasoning and content_value is None and reasoning_content:
                            # 添加<thinking>标签
                            thinking_tag = "<thinking>\n"
                            # 更新buffer和full_content
                            buffer += thinking_tag
                            full_content += thinking_tag
                            # 发送标签和reasoning_content
                            combined_content = thinking_tag + reasoning_content
                            if DEBUG_LLM_RESPONSE:
                                logger.debug(f"[LLM流式响应] 发送thinking开始标签和内容: '{combined_content}'")
                            callback(combined_content)
                            has_reasoning = True
                        else:
                            # 直接发送reasoning_content，但只有当内容不是纯空白字符时才发送
                            if reasoning_content and reasoning_content.strip():
                                if DEBUG_LLM_RESPONSE:
                                    logger.debug(f"[LLM流式响应] 直接发送reasoning_content: '{reasoning_content}'")
                                callback(reasoning_content)
                            else:
                                if DEBUG_LLM_RESPONSE:
                                    logger.debug(f"[LLM流式响应] 跳过空白reasoning_content: '{reasoning_content}'")
                                # 仍然需要将内容添加到buffer和full_content中，即使不发送给前端

                        # 无论是否是开始，都将reasoning_content添加到buffer和full_content
                        buffer += reasoning_content
                        full_content += reasoning_content

                except json.JSONDecodeError as e:
                    if DEBUG_LLM_RESPONSE:
                        logger.debug(f"[LLM流式响应] JSON解析错误: {e}, 原始内容: {content}")
                    continue
    except StreamCancelledException as e:
        # 流式处理被主动取消异常
        logger.info(f"[LLM流式响应] 流式处理被主动取消: {e.request_id}")
        is_cancelled = True
        cancelled_agent_id = e.agent_id
        # 直接返回，不需要进一步处理
        return ""
    except socket.timeout:
        # Socket超时异常，区分取消操作和真正的网络超时
        logger.info("[LLM流式响应] Socket超时，检查是否为取消操作")
        # 检查是否确实被取消
        try:
            if check_for_cancel_signal():
                logger.info("[LLM流式响应] 确认是取消操作导致的超时")
                is_cancelled = True
            else:
                # 如果不是取消操作，这可能是真正的网络超时
                if is_cancelled:
                    logger.info("[LLM流式响应] 已标记为取消状态，超时是预期的")
                else:
                    logger.warning("[LLM流式响应] Socket超时但未检测到取消信号，可能是网络问题或模型响应过慢")
                    # 对于真正的网络超时，通知用户
                    if callback and not is_cancelled:
                        callback(f"\n[警告] 模型响应超时，可能是网络问题或模型处理时间过长")
        except StreamCancelledException as e:
            logger.info(f"[LLM流式响应] 在超时检查中检测到取消: {e.request_id}")
            is_cancelled = True
            cancelled_agent_id = e.agent_id
            return ""
    except (requests.exceptions.RequestException, AttributeError) as e:
        # 捕获请求异常和属性错误（可能是由于连接被关闭）
        error_str = str(e)

        # 检查是否是由于取消导致的特定错误
        if is_cancelled or "'NoneType' object has no attribute 'read'" in error_str or "Connection aborted" in error_str or "Connection reset" in error_str or "timeout" in error_str.lower():
            # 这是一个预期的错误，发生在我们成功关闭HTTP连接后
            logger.info(f"[LLM流式响应] 流式处理被取消，异常信息: {error_str}")

            # 如果是由于取消导致的错误，但尚未标记为已取消，则标记为已取消
            if not is_cancelled:
                is_cancelled = True
                logger.info("[LLM流式响应] 根据异常信息判断流式处理已被取消")
        else:
            logger.error(f"[LLM流式响应] 流式处理出错: {error_str}\n{traceback.format_exc()}")
            # 如果不是由于取消导致的错误，通知用户
            if callback and not is_cancelled:
                callback(f"\n[错误] 流式处理出错: {error_str}")
    except Exception as e:
        # 捕获其他所有异常
        error_str = str(e)

        # 检查是否是由于取消导致的特定错误
        if is_cancelled or "'NoneType' object has no attribute 'read'" in error_str or "Connection aborted" in error_str or "Connection reset" in error_str:
            # 这是一个预期的错误，发生在我们成功关闭HTTP连接后
            logger.info(f"[LLM流式响应] 流式处理被取消，异常信息: {error_str}")

            # 如果是由于取消导致的错误，但尚未标记为已取消，则标记为已取消
            if not is_cancelled:
                is_cancelled = True
                logger.info("[LLM流式响应] 根据异常信息判断流式处理已被取消")
        else:
            logger.error(f"[LLM流式响应] 未预期的错误: {error_str}\n{traceback.format_exc()}")
            # 通知用户
            if callback and not is_cancelled:
                callback(f"\n[错误] 未预期的错误: {error_str}")
    # 如果有未关闭的reasoning标签，添加结束标签
    if has_reasoning:
        if DEBUG_LLM_RESPONSE:
            logger.debug("[LLM流式响应] 流式响应结束时添加reasoning结束标签</thinking>")

        # 添加结束标签
        thinking_end_tag = "\n</thinking>"
        # 更新full_content
        full_content += thinking_end_tag
        # 发送结束标签
        callback(thinking_end_tag)
    # 如果流式处理被取消，返回已处理的内容
    if is_cancelled:
        logger.info(f"[LLM流式响应] 流式处理被取消，已处理内容长度: {len(full_content)}")
        return full_content

    # 流式响应完成后，处理工具调用
    if DEBUG_LLM_RESPONSE:
        logger.debug("[LLM流式响应] 流式输出完成，开始处理工具调用")

    # 提取工具调用
    tool_call_content = []
    tool_result_content = []

    # 处理XML格式的工具调用
    xml_tool_calls = parse_tool_calls(full_content)
    if xml_tool_calls:
        if DEBUG_LLM_RESPONSE:
            logger.debug(f"[LLM流式响应] 解析出 {len(xml_tool_calls)} 个XML格式工具调用")

        for tool_call in xml_tool_calls:
            # 每处理一个工具调用，检查是否有取消信号
            if check_for_cancel_signal():
                logger.info("[LLM流式响应] 收到取消信号，中断工具调用处理")
                return full_content

            # 执行工具调用前，确保 tool_call 有一个有效的 ID
            if 'id' not in tool_call or not tool_call['id']:
                tool_call['id'] = str(uuid.uuid4())

            # 不再通过SSE发送工具调用信息，只在内部记录
            # 注释掉以下代码，避免向前端发送ToolCallAction消息
            # tool_call_message = format_tool_call(
            #     function_name=tool_call["function"]["name"],
            #     arguments=tool_call["function"]["arguments"],
            #     tool_call_id=tool_call['id']
            # )
            # tool_call_str = serialize_message(tool_call_message)
            # callback(tool_call_str)
            # 不再需要向full_content添加工具调用的HTML注释
            # 前端可以根据ToolCallResult中的信息正确显示工具调用结果

            # 执行工具调用
            tool_result = execute_tool_call(tool_call)

            # 打印工具调用结果，用于调试
            logger.debug(f"[LLM流式响应] 工具 {tool_call['function']['name']} 调用结果: {tool_result[:200]}{'...' if len(tool_result) > 200 else ''}")

            # 检查工具调用结果中是否包含isError字段或错误信息
            status = "success"
            try:
                # 尝试解析工具调用结果
                result_obj = json.loads(tool_result) if isinstance(tool_result, str) else tool_result

                # 检查是否包含错误信息
                if isinstance(result_obj, dict):
                    # 如果是HTTP错误且状态码不是200，应该标记为错误
                    if result_obj.get('error_type') == 'HTTPError' and '状态码: 200' not in str(result_obj.get('error', '')):
                        status = "error"
                        logger.debug(f"[LLM流式响应] 工具 {tool_call['function']['name']} HTTP调用返回非200状态码: {tool_result[:200]}{'...' if len(tool_result) > 200 else ''}")
                    # 如果包含is_error=True，也应该标记为错误（除非error=False）
                    elif result_obj.get('isError') == True or result_obj.get('is_error') == True:
                        # 特殊处理：如果error值为False，不应该被视为错误
                        if 'error' in result_obj and result_obj['error'] is False:
                            logger.debug(f"[LLM流式响应] 工具 {tool_call['function']['name']} 返回error=False，视为成功响应")
                        else:
                            status = "error"
                            logger.debug(f"[LLM流式响应] 工具 {tool_call['function']['name']} 调用失败: {tool_result[:200]}{'...' if len(tool_result) > 200 else ''}")
                    # 如果包含error字段且不是False，应该标记为错误
                    elif 'error' in result_obj and result_obj['error'] is not False and result_obj['error'] != '':
                        status = "error"
                        logger.debug(f"[LLM流式响应] 工具 {tool_call['function']['name']} 返回错误信息: {result_obj['error']}")
            except Exception as e:
                # 如果解析失败，检查结果字符串是否包含错误信息
                if isinstance(tool_result, str) and ('错误' in tool_result or 'Error' in tool_result or 'error' in tool_result or '失败' in tool_result) and 'error=False' not in tool_result and '"error":false' not in tool_result.lower():
                    status = "error"
                    logger.debug(f"[LLM流式响应] 工具 {tool_call['function']['name']} 调用可能失败: {tool_result[:200]}{'...' if len(tool_result) > 200 else ''}")
                logger.debug(f"[LLM流式响应] 解析工具调用结果时出错: {e}")

            # 发送工具调用结果，包含工具参数 - 使用role:tool格式
            tool_result_message = format_tool_result_as_role(
                result=tool_result,
                tool_name=tool_call['function']['name'],
                tool_call_id=tool_call['id'],
                tool_parameter=tool_call['function']['arguments'],
                status=status
            )
            tool_result_str = serialize_message(tool_result_message)
            callback(tool_result_str)
            # 将工具结果添加到full_content
            full_content += tool_result_str

            # 收集工具调用和结果
            tool_call_content.append(tool_call)
            tool_result_content.append({
                "tool_call_id": tool_call['id'],
                "tool_name": tool_call['function']['name'],
                "result": tool_result
            })

    # 处理OpenAI格式的工具调用
    if openai_tool_calls:
        if DEBUG_LLM_RESPONSE:
            logger.debug(f"[LLM流式响应] 处理 {len(openai_tool_calls)} 个OpenAI格式工具调用")

        for openai_tool_call in openai_tool_calls:
            # 每处理一个工具调用，检查是否有取消信号
            if check_for_cancel_signal():
                logger.info("[LLM流式响应] 收到取消信号，中断OpenAI工具调用处理")
                return full_content

            # 检查工具调用是否完整
            if not openai_tool_call['function']['name'] or not openai_tool_call['function']['arguments']:
                if DEBUG_LLM_RESPONSE:
                    logger.debug(f"[LLM流式响应] 跳过不完整的OpenAI工具调用: {openai_tool_call}")
                continue

            # 修正参数格式 (确保是有效的JSON字符串)
            try:
                # 尝试解析参数，如果已经是JSON字符串则无需修改
                json.loads(openai_tool_call['function']['arguments'])
            except json.JSONDecodeError:
                # 如果不是有效的JSON，进行格式化修正
                try:
                    fixed_args = openai_tool_call['function']['arguments']
                    if not fixed_args.startswith('{'):
                        fixed_args = '{' + fixed_args
                    if not fixed_args.endswith('}'):
                        fixed_args = fixed_args + '}'
                    json.loads(fixed_args)  # 验证修复后的格式
                    openai_tool_call['function']['arguments'] = fixed_args
                except:
                    # 如果修复失败，将参数包装为基本JSON
                    openai_tool_call['function']['arguments'] = f'{{"value": "{openai_tool_call["function"]["arguments"]}"}}'

            # 执行工具调用前，确保 openai_tool_call 有一个有效的 ID
            if 'id' not in openai_tool_call or not openai_tool_call['id']:
                openai_tool_call['id'] = str(uuid.uuid4())

            # 不再通过SSE发送工具调用信息，只在内部记录
            # 注释掉以下代码，避免向前端发送ToolCallAction消息
            # tool_call_message = format_tool_call(
            #     function_name=openai_tool_call["function"]["name"],
            #     arguments=openai_tool_call["function"]["arguments"],
            #     tool_call_id=openai_tool_call['id']
            # )
            # tool_call_str = serialize_message(tool_call_message)
            # callback(tool_call_str)
            # 不再需要向full_content添加工具调用的HTML注释
            # 前端可以根据ToolCallResult中的信息正确显示工具调用结果

            # 执行工具调用
            tool_result = execute_tool_call(openai_tool_call)

            # 打印工具调用结果，用于调试
            logger.debug(f"[LLM流式响应] OpenAI工具 {openai_tool_call['function']['name']} 调用结果: {tool_result[:200]}{'...' if len(tool_result) > 200 else ''}")

            # 检查工具调用结果中是否包含isError字段或错误信息
            status = "success"
            try:
                # 尝试解析工具调用结果
                result_obj = json.loads(tool_result) if isinstance(tool_result, str) else tool_result

                # 检查是否包含错误信息
                if isinstance(result_obj, dict):
                    # 如果是HTTP错误且状态码不是200，应该标记为错误
                    if result_obj.get('error_type') == 'HTTPError' and '状态码: 200' not in str(result_obj.get('error', '')):
                        status = "error"
                        logger.debug(f"[LLM流式响应] OpenAI工具 {openai_tool_call['function']['name']} HTTP调用返回非200状态码: {tool_result[:200]}{'...' if len(tool_result) > 200 else ''}")
                    # 如果包含is_error=True，也应该标记为错误（除非error=False）
                    elif result_obj.get('isError') == True or result_obj.get('is_error') == True:
                        # 特殊处理：如果error值为False，不应该被视为错误
                        if 'error' in result_obj and result_obj['error'] is False:
                            logger.debug(f"[LLM流式响应] OpenAI工具 {openai_tool_call['function']['name']} 返回error=False，视为成功响应")
                        else:
                            status = "error"
                            logger.debug(f"[LLM流式响应] OpenAI工具 {openai_tool_call['function']['name']} 调用失败: {tool_result[:200]}{'...' if len(tool_result) > 200 else ''}")
                    # 如果包含error字段且不是False，应该标记为错误
                    elif 'error' in result_obj and result_obj['error'] is not False and result_obj['error'] != '':
                        status = "error"
                        logger.debug(f"[LLM流式响应] OpenAI工具 {openai_tool_call['function']['name']} 返回错误信息: {result_obj['error']}")
            except Exception as e:
                # 如果解析失败，检查结果字符串是否包含错误信息
                if isinstance(tool_result, str) and ('错误' in tool_result or 'Error' in tool_result or 'error' in tool_result or '失败' in tool_result) and 'error=False' not in tool_result and '"error":false' not in tool_result.lower():
                    status = "error"
                    logger.debug(f"[LLM流式响应] OpenAI工具 {openai_tool_call['function']['name']} 调用可能失败: {tool_result[:200]}{'...' if len(tool_result) > 200 else ''}")
                logger.debug(f"[LLM流式响应] 解析OpenAI工具调用结果时出错: {e}")

            # 发送工具调用结果，包含工具参数 - 使用role:tool格式
            tool_result_message = format_tool_result_as_role(
                result=tool_result,
                tool_name=openai_tool_call['function']['name'],
                tool_call_id=openai_tool_call['id'],
                tool_parameter=openai_tool_call['function']['arguments'],
                status=status
            )
            tool_result_str = serialize_message(tool_result_message)
            callback(tool_result_str)
            # 将工具结果添加到full_content
            full_content += tool_result_str

            # 收集工具调用和结果
            tool_call_content.append(openai_tool_call)
            tool_result_content.append({
                "tool_call_id": openai_tool_call['id'],
                "tool_name": openai_tool_call['function']['name'],
                "result": tool_result
            })

    # 解析JSON格式的工具调用
    json_tool_pattern = r'(\{[\s\S]*?"function"\s*:\s*\{[\s\S]*?"name"\s*:\s*"[^"]+?"[\s\S]*?\}[\s\S]*?\})'
    json_tool_matches = re.findall(json_tool_pattern, full_content)

    for json_tool_str in json_tool_matches:
        # 每处理一个工具调用，检查是否有取消信号
        if check_for_cancel_signal():
            logger.info("[LLM流式响应] 收到取消信号，中断JSON工具调用处理")
            return full_content

        try:
            # 尝试解析工具调用
            tool_call = json.loads(json_tool_str)
            if 'function' in tool_call and 'name' in tool_call['function'] and 'arguments' in tool_call['function']:
                # 检查是否已处理过相同的工具调用 (避免重复处理)
                tool_name = tool_call['function']['name']
                tool_id = tool_call.get('id', '')

                # 跳过重复工具调用
                if any(tc.get('id', '') == tool_id for tc in tool_call_content) or \
                   any(tc['function']['name'] == tool_name and
                       tc['function']['arguments'] == tool_call['function']['arguments'] for tc in tool_call_content):
                    continue

                # 执行工具调用前，确保 tool_call 有一个有效的 ID
                if 'id' not in tool_call or not tool_call['id']:
                    tool_call['id'] = str(uuid.uuid4())

                tool_id = tool_call['id']

                # 不再通过SSE发送工具调用信息，只在内部记录
                # 注释掉以下代码，避免向前端发送ToolCallAction消息
                # tool_call_message = format_tool_call(
                #     function_name=tool_call["function"]["name"],
                #     arguments=tool_call["function"]["arguments"],
                #     tool_call_id=tool_call['id']
                # )
                # tool_call_str = serialize_message(tool_call_message)
                # callback(tool_call_str)
                # 不再需要向full_content添加工具调用的HTML注释
                # 前端可以根据ToolCallResult中的信息正确显示工具调用结果

                # 执行工具调用
                tool_result = execute_tool_call(tool_call)

                # 打印工具调用结果，用于调试
                logger.debug(f"[LLM流式响应] JSON工具 {tool_call['function']['name']} 调用结果: {tool_result[:200]}{'...' if len(tool_result) > 200 else ''}")

                # 检查工具调用结果中是否包含isError字段或错误信息
                status = "success"
                try:
                    # 尝试解析工具调用结果
                    result_obj = json.loads(tool_result) if isinstance(tool_result, str) else tool_result

                    # 检查是否包含错误信息
                    if isinstance(result_obj, dict):
                        # 如果是HTTP错误且状态码不是200，应该标记为错误
                        if result_obj.get('error_type') == 'HTTPError' and '状态码: 200' not in str(result_obj.get('error', '')):
                            status = "error"
                            logger.debug(f"[LLM流式响应] JSON工具 {tool_call['function']['name']} HTTP调用返回非200状态码: {tool_result[:200]}{'...' if len(tool_result) > 200 else ''}")
                        # 如果包含is_error=True，也应该标记为错误（除非error=False）
                        elif result_obj.get('isError') == True or result_obj.get('is_error') == True:
                            # 特殊处理：如果error值为False，不应该被视为错误
                            if 'error' in result_obj and result_obj['error'] is False:
                                logger.debug(f"[LLM流式响应] JSON工具 {tool_call['function']['name']} 返回error=False，视为成功响应")
                            else:
                                status = "error"
                                logger.debug(f"[LLM流式响应] JSON工具 {tool_call['function']['name']} 调用失败: {tool_result[:200]}{'...' if len(tool_result) > 200 else ''}")
                        # 如果包含error字段且不是False，应该标记为错误
                        elif 'error' in result_obj and result_obj['error'] is not False and result_obj['error'] != '':
                            status = "error"
                            logger.debug(f"[LLM流式响应] JSON工具 {tool_call['function']['name']} 返回错误信息: {result_obj['error']}")
                except Exception as e:
                    # 如果解析失败，检查结果字符串是否包含错误信息
                    if isinstance(tool_result, str) and ('错误' in tool_result or 'Error' in tool_result or 'error' in tool_result or '失败' in tool_result) and 'error=False' not in tool_result and '"error":false' not in tool_result.lower():
                        status = "error"
                        logger.debug(f"[LLM流式响应] JSON工具 {tool_call['function']['name']} 调用可能失败: {tool_result[:200]}{'...' if len(tool_result) > 200 else ''}")
                    logger.debug(f"[LLM流式响应] 解析JSON工具调用结果时出错: {e}")

                # 发送工具调用结果，包含工具参数 - 使用role:tool格式
                tool_result_message = format_tool_result_as_role(
                    result=tool_result,
                    tool_name=tool_call['function']['name'],
                    tool_call_id=tool_call['id'],
                    tool_parameter=tool_call['function']['arguments'],
                    status=status
                )
                tool_result_str = serialize_message(tool_result_message)
                callback(tool_result_str)
                # 将工具结果添加到full_content
                full_content += tool_result_str

                # 收集工具调用和结果
                tool_call_content.append(tool_call)
                tool_result_content.append({
                    "tool_call_id": tool_call['id'],
                    "tool_name": tool_call['function']['name'],
                    "result": tool_result
                })
        except json.JSONDecodeError:
            if DEBUG_LLM_RESPONSE:
                logger.debug(f"[LLM流式响应] JSON格式工具调用解析失败: {json_tool_str}")

    # 再次检查是否有取消信号，如果有则跳过二次LLM调用
    if check_for_cancel_signal():
        logger.info("[LLM流式响应] 收到取消信号，跳过二次LLM调用")
        return full_content

    # 检查是否需要再次调用LLM
    if tool_call_content and tool_result_content and original_messages and api_config:
        if DEBUG_LLM_RESPONSE:
            logger.debug("\n[LLM流式响应] 检测到工具调用和结果，将再次调用LLM")
            logger.debug(f"[LLM流式响应] 工具调用数量: {len(tool_call_content)}")
            logger.debug(f"[LLM流式响应] 工具结果数量: {len(tool_result_content)}")

        # 不再需要添加HTML注释分隔符
        # 前端可以通过消息类型识别工具调用和结果

        # 调用二次LLM处理
        second_response = call_llm_with_tool_results(
            original_messages=original_messages,
            tool_calls=tool_call_content,
            tool_results=tool_result_content,
            api_config=api_config,
            callback=callback
        )

        # 不再需要添加HTML注释完成标记
        # 前端可以通过消息类型识别工具调用和结果处理的完成

        if DEBUG_LLM_RESPONSE:
            logger.debug("\n[LLM流式响应] 二次调用LLM完成")

        # 将二次调用的结果添加到完整的响应中
        if second_response:
            full_content += f"\n{second_response}"

    if DEBUG_LLM_RESPONSE:
        logger.debug("-"*40)
        logger.debug(f"[LLM流式响应] 完成处理，总内容长度: {len(full_content)}")
        logger.debug(f"[LLM流式响应] 工具调用数量: {len(tool_call_content)}")
        logger.debug(f"[LLM流式响应] 工具结果数量: {len(tool_result_content)}")
        logger.debug("="*40 + "\n")

    return full_content


