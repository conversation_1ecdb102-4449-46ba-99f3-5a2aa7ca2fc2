from datetime import datetime
from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, ForeignKey, JSON, Float, Table
from sqlalchemy.orm import relationship
from app.extensions import db
from werkzeug.security import generate_password_hash, check_password_hash
from app.utils.datetime_utils import get_current_time_with_timezone

class BaseMixin:
    # 保留id和时间戳，但不设置__tablename__，让SQLAlchemy使用默认的表名
    id = Column(Integer, primary_key=True)
    created_at = Column(DateTime, default=get_current_time_with_timezone)
    updated_at = Column(DateTime, default=get_current_time_with_timezone, onupdate=get_current_time_with_timezone)

class User(BaseMixin, db.Model):
    __tablename__ = 'users'
    username = Column(String(64), unique=True, nullable=False)
    password_hash = Column(String(128))
    email = Column(String(120), unique=True)
    is_active = Column(Boolean, default=True)

    action_tasks = relationship("ActionTask", back_populates="user")

    def __repr__(self):
        return f'<User {self.username}>'

    def set_password(self, password):
        """设置用户密码"""
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        """检查用户密码"""
        return check_password_hash(self.password_hash, password)

# 行动空间模型
class ActionSpace(BaseMixin, db.Model):
    __tablename__ = 'action_spaces'
    name = Column(String(100), nullable=False)
    description = Column(Text)
    settings = Column(JSON, default=dict)

    # 关联关系
    rule_sets = relationship("ActionSpaceRuleSet", back_populates="action_space")
    action_tasks = relationship("ActionTask", back_populates="action_space")
    tags = relationship("ActionSpaceTag", back_populates="action_space")
    roles = relationship("ActionSpaceRole", back_populates="action_space")  # 添加角色关联
    observers = relationship("ActionSpaceObserver", back_populates="action_space")  # 添加监督者关联

    def __repr__(self):
        return f'<ActionSpace {self.name}>'

# 规则模型
class Rule(BaseMixin, db.Model):
    __tablename__ = 'rules'
    name = Column(String(100), nullable=False)
    description = Column(Text)
    content = Column(Text, nullable=False)  # 规则的具体内容
    category = Column(String(50))  # 规则类别，如：interaction, evaluation, constraint
    type = Column(String(20), default='llm')  # 规则类型: llm（自然语言规则）或 logic（逻辑规则）
    is_active = Column(Boolean, default=True)
    settings = Column(JSON, default=dict)

    # 关联关系
    rule_sets = relationship("RuleSetRule", back_populates="rule")

    def __repr__(self):
        return f'<Rule {self.name}>'

# 规则触发记录模型
class RuleTriggerLog(BaseMixin, db.Model):
    __tablename__ = 'rule_trigger_logs'
    rule_id = Column(Integer, ForeignKey('rules.id'), nullable=False)
    action_task_id = Column(Integer, ForeignKey('action_tasks.id'), nullable=False)
    conversation_id = Column(Integer, ForeignKey('conversations.id'))
    trigger_type = Column(String(20), default='manual')  # manual, automatic, scheduled
    trigger_source = Column(String(50))  # user, system, supervisor, agent
    context = Column(Text)  # 触发时的上下文信息
    variables = Column(JSON, default=dict)  # 触发时的变量值
    result = Column(JSON)  # 检查结果
    passed = Column(Boolean)  # 是否通过检查
    message = Column(Text)  # 检查消息
    details = Column(Text)  # 详细信息
    execution_time = Column(Float)  # 执行耗时(秒)

    # 关联关系
    rule = relationship("Rule")
    action_task = relationship("ActionTask")
    conversation = relationship("Conversation")

    def __repr__(self):
        return f'<RuleTriggerLog {self.rule_id}:{self.action_task_id}>'

# 规则集与规则的多对多关联表
class RuleSetRule(BaseMixin, db.Model):
    __tablename__ = 'rule_set_rules'
    rule_set_id = Column(Integer, ForeignKey('rule_sets.id'), nullable=False)
    rule_id = Column(Integer, ForeignKey('rules.id'), nullable=False)
    priority = Column(Integer, default=0)  # 规则在规则集中的优先级

    rule_set = relationship("RuleSet", back_populates="rules_relation")
    rule = relationship("Rule", back_populates="rule_sets")

    def __repr__(self):
        return f'<RuleSetRule {self.rule_set_id}:{self.rule_id}>'

class RuleSet(BaseMixin, db.Model):
    __tablename__ = 'rule_sets'
    name = Column(String(100), nullable=False)
    description = Column(Text)
    rules = Column(JSON, default=list)  # 保留原来的rules字段，用于兼容性
    conditions = Column(JSON, default=list)
    actions = Column(JSON, default=list)
    settings = Column(JSON, default=dict)

    # 新增的关联关系
    rules_relation = relationship("RuleSetRule", back_populates="rule_set")
    # 添加多对多关系
    action_spaces = relationship("ActionSpaceRuleSet", back_populates="rule_set")

    def __repr__(self):
        return f'<RuleSet {self.name}>'

class Role(BaseMixin, db.Model):
    __tablename__ = 'roles'
    name = Column(String(100), nullable=False)
    description = Column(Text)
    system_prompt = Column(Text)
    avatar = Column(String(255))
    settings = Column(JSON)
    is_predefined = Column(Boolean, default=False)
    model = Column(Integer, nullable=True)  # 关联的模型配置ID
    is_observer_role = Column(Boolean, default=False)  # 是否为监督者角色
    source = Column(String(20), default='internal')  # 角色来源：internal(内部)或external(外部)

    # 角色级别的模型参数（从模型配置继承并可覆盖）
    temperature = Column(Float, default=0.7)  # 温度参数，控制随机性
    top_p = Column(Float, default=1.0)  # Top-P采样参数
    frequency_penalty = Column(Float, default=0.0)  # 频率惩罚
    presence_penalty = Column(Float, default=0.0)  # 存在惩罚
    stop_sequences = Column(JSON, default=list)  # 停止序列

    # 关联关系
    knowledge_bases = relationship("RoleKnowledge", back_populates="role")
    tools = relationship("RoleTool", back_populates="role")

    capabilities = relationship("RoleCapability", back_populates="role")  # 新增能力关联
    agents = relationship("Agent", back_populates="role")
    action_spaces = relationship("ActionSpaceRole", back_populates="role")  # 添加行动空间关联

    def __repr__(self):
        return f'<Role {self.name}>'

class Knowledge(BaseMixin, db.Model):
    __tablename__ = 'knowledges'
    name = Column(String(100), nullable=False)
    description = Column(Text)
    type = Column(String(50))  # 知识库类型标识，统一为'knowledge'
    content = Column(Text)
    settings = Column(JSON)

    roles = relationship("RoleKnowledge", back_populates="knowledge")

    def __repr__(self):
        return f'<Knowledge {self.name}>'

# 图谱增强配置模型
class GraphEnhancement(BaseMixin, db.Model):
    __tablename__ = 'graph_enhancements'

    # 基础配置
    enabled = Column(Boolean, default=False)  # 是否启用图谱增强
    framework = Column(String(50), default='graphiti')  # RAG框架: lightrag, graphiti, graphrag
    name = Column(String(100), nullable=False)  # 配置名称
    description = Column(Text)  # 配置描述

    # 所有其他配置都存储在这个JSON字段中
    framework_config = Column(JSON, default=dict)  # 框架特定配置参数

    def __repr__(self):
        return f'<GraphEnhancement {self.name}>'

class Tool(BaseMixin, db.Model):
    __tablename__ = 'tools'
    name = Column(String(100), nullable=False)
    description = Column(Text)
    type = Column(String(50))
    config = Column(JSON)
    settings = Column(JSON)

    roles = relationship("RoleTool", back_populates="tool")

    def __repr__(self):
        return f'<Tool {self.name}>'

class Capability(BaseMixin, db.Model):
    __tablename__ = 'capabilities'
    name = Column(String(100), nullable=False)
    description = Column(Text)
    type = Column(String(50))  # 能力类型，如：text, vision, code等
    provider = Column(String(50))  # 提供商
    parameters = Column(JSON)  # 输入参数定义
    response_format = Column(JSON)  # 响应格式定义
    examples = Column(JSON)  # 示例
    settings = Column(JSON)  # 额外设置
    tools = Column(JSON, default=dict)  # 存储能力与工具/MCP服务器的关联关系，格式: {"server1": ["tool1", "tool2"], "server2": ["tool2"]}
    security_level = Column(Integer, default=1)  # 安全级别: 1=低风险, 2=中风险, 3=高风险
    default_enabled = Column(Boolean, default=False)  # 是否默认启用
    icon = Column(String(50))  # 图标名称

    # 关联关系
    roles = relationship("RoleCapability", back_populates="capability")

    def __repr__(self):
        return f'<Capability {self.name}>'

class RoleCapability(BaseMixin, db.Model):
    __tablename__ = 'role_capabilities'
    role_id = Column(Integer, ForeignKey('roles.id'), nullable=False)
    capability_id = Column(Integer, ForeignKey('capabilities.id'), nullable=False)

    role = relationship("Role", back_populates="capabilities")
    capability = relationship("Capability", back_populates="roles")

    def __repr__(self):
        return f'<RoleCapability {self.role_id}:{self.capability_id}>'



class WorkspaceTemplate(BaseMixin, db.Model):
    __tablename__ = 'workspace_templates'
    name = Column(String(100), nullable=False)
    description = Column(Text)
    category = Column(String(50), nullable=False)  # shared, agent, skills
    content = Column(Text, nullable=False)
    source_file_path = Column(String(500))  # 源文件路径（如果从文件创建）
    is_active = Column(Boolean, default=True)
    settings = Column(JSON, default=dict)

    def __repr__(self):
        return f'<WorkspaceTemplate {self.name}>'

class RoleKnowledge(BaseMixin, db.Model):
    __tablename__ = 'role_knowledges'
    role_id = Column(Integer, ForeignKey('roles.id'), nullable=False)
    knowledge_id = Column(Integer, ForeignKey('knowledges.id'), nullable=False)

    role = relationship("Role", back_populates="knowledge_bases")
    knowledge = relationship("Knowledge", back_populates="roles")

    def __repr__(self):
        return f'<RoleKnowledge {self.role_id}:{self.knowledge_id}>'

class RoleTool(BaseMixin, db.Model):
    __tablename__ = 'role_tools'
    role_id = Column(Integer, ForeignKey('roles.id'), nullable=False)
    tool_id = Column(Integer, ForeignKey('tools.id'), nullable=False)

    role = relationship("Role", back_populates="tools")
    tool = relationship("Tool", back_populates="roles")

    def __repr__(self):
        return f'<RoleTool {self.role_id}:{self.tool_id}>'



# 外部知识库提供商表
class ExternalKnowledgeProvider(BaseMixin, db.Model):
    __tablename__ = 'external_kb_providers'
    name = Column(String(100), nullable=False)
    type = Column(String(50), nullable=False)  # dify, ragflow, fastgpt, custom
    base_url = Column(String(500), nullable=False)
    api_key = Column(String(500), nullable=False)
    config = Column(JSON)  # 其他配置信息
    status = Column(String(20), default='active')  # active, inactive

    # 关联关系
    external_knowledges = relationship("ExternalKnowledge", back_populates="provider", cascade="all, delete-orphan")

    def __repr__(self):
        return f'<ExternalKnowledgeProvider {self.name}>'

# 外部知识库表
class ExternalKnowledge(BaseMixin, db.Model):
    __tablename__ = 'external_knowledges'
    name = Column(String(100), nullable=False)
    description = Column(Text)
    provider_id = Column(Integer, ForeignKey('external_kb_providers.id'), nullable=False)
    external_kb_id = Column(String(100), nullable=False)  # 外部系统中的知识库ID
    query_config = Column(JSON)  # 查询配置
    status = Column(String(20), default='active')  # active, inactive

    # 关联关系
    provider = relationship("ExternalKnowledgeProvider", back_populates="external_knowledges")
    roles = relationship("RoleExternalKnowledge", back_populates="external_knowledge", cascade="all, delete-orphan")

    def __repr__(self):
        return f'<ExternalKnowledge {self.name}>'

# 角色外部知识库关联表
class RoleExternalKnowledge(BaseMixin, db.Model):
    __tablename__ = 'role_external_knowledges'
    role_id = Column(Integer, ForeignKey('roles.id'), nullable=False)
    external_knowledge_id = Column(Integer, ForeignKey('external_knowledges.id'), nullable=False)
    config = Column(JSON)  # 角色特定的查询配置

    # 关联关系
    role = relationship("Role")
    external_knowledge = relationship("ExternalKnowledge", back_populates="roles")

    # 唯一约束
    __table_args__ = (db.UniqueConstraint('role_id', 'external_knowledge_id', name='_role_external_knowledge_uc'),)

    def __repr__(self):
        return f'<RoleExternalKnowledge {self.role_id}:{self.external_knowledge_id}>'

# 外部知识库查询日志表
class ExternalKnowledgeQueryLog(BaseMixin, db.Model):
    __tablename__ = 'external_kb_query_logs'
    external_knowledge_id = Column(Integer, ForeignKey('external_knowledges.id'), nullable=False)
    role_id = Column(Integer, ForeignKey('roles.id'))
    query_text = Column(Text, nullable=False)
    response_data = Column(JSON)
    query_time = Column(Float)  # 查询耗时(秒)
    status = Column(String(20))  # success, error, timeout
    error_message = Column(Text)

    # 关联关系
    external_knowledge = relationship("ExternalKnowledge")
    role = relationship("Role")

    def __repr__(self):
        return f'<ExternalKnowledgeQueryLog {self.external_knowledge_id}>'

class Agent(BaseMixin, db.Model):
    __tablename__ = 'agents'
    name = Column(String(100), nullable=False)
    description = Column(Text)
    avatar = Column(String(255))
    settings = Column(JSON)
    status = Column(String(20), default='active')
    action_task_id = Column(Integer, ForeignKey('action_tasks.id'))
    role_id = Column(Integer, ForeignKey('roles.id'), nullable=False)
    type = Column(String(20), default='agent')
    source = Column(String(20), default='internal')  # 智能体来源：internal(内部)或external(外部)
    additional_prompt = Column(Text, default='')  # 额外提示词，从行动空间角色中获取
    is_observer = Column(Boolean, default=False)  # 是否为监督者

    role = relationship("Role", back_populates="agents")
    action_task = relationship("ActionTask", back_populates="direct_agents")
    action_task_agents = relationship("ActionTaskAgent", back_populates="agent")
    messages = relationship("Message", back_populates="agent")
    conversation_agents = relationship("ConversationAgent", back_populates="agent")
    variables = relationship("AgentVariable", back_populates="agent")  # 新增代理变量关联

    def __repr__(self):
        return f'<Agent {self.name}>'

class AgentVariable(BaseMixin, db.Model):
    """代理变量模型，用于存储每个代理的特定变量"""
    __tablename__ = 'agent_variables'
    name = Column(String(100), nullable=False)
    label = Column(String(100))  # 变量标签，用于前端显示
    value = Column(Text)
    type = Column(String(20), default='text')  # 只保留text类型
    history = Column(JSON, default=list)  # 历史记录
    is_public = Column(Boolean, default=True)  # 是否对其他代理公开

    agent_id = Column(Integer, ForeignKey('agents.id', ondelete='CASCADE'), nullable=False)
    agent = relationship("Agent", back_populates="variables")

    def __repr__(self):
        return f'<AgentVariable {self.name} for agent {self.agent_id}>'

class ActionTask(BaseMixin, db.Model):
    __tablename__ = 'action_tasks'
    title = Column(String(100), nullable=False)
    description = Column(Text)
    status = Column(String(20), default='active')  # active, completed, terminated
    mode = Column(String(20), default='sequential')  # sequential, panel, debate, collaborative
    rule_set_id = Column(Integer, ForeignKey('rule_sets.id'))

    user_id = Column(Integer, ForeignKey('users.id'))
    action_space_id = Column(Integer, ForeignKey('action_spaces.id'))

    user = relationship("User", back_populates="action_tasks")
    action_space = relationship("ActionSpace", back_populates="action_tasks")
    agents = relationship("ActionTaskAgent", back_populates="action_task")
    direct_agents = relationship("Agent", back_populates="action_task")
    messages = relationship("Message", back_populates="action_task")
    environment_variables = relationship("ActionTaskEnvironmentVariable", back_populates="action_task")
    conversations = relationship("Conversation", back_populates="action_task")

    def __repr__(self):
        return f'<ActionTask {self.title}>'

class Conversation(BaseMixin, db.Model):
    __tablename__ = 'conversations'
    title = Column(String(100), nullable=False)
    description = Column(Text)
    status = Column(String(20), default='active')  # active, completed, terminated
    mode = Column(String(20), default='sequential')  # sequential, panel, debate, collaborative

    action_task_id = Column(Integer, ForeignKey('action_tasks.id'), nullable=False)

    action_task = relationship("ActionTask", back_populates="conversations")
    agents = relationship("ConversationAgent", back_populates="conversation")
    messages = relationship("Message", back_populates="conversation")
    autonomous_tasks = relationship("AutonomousTask", back_populates="conversation", cascade="all, delete-orphan")

    def __repr__(self):
        return f'<Conversation {self.title}>'

class ConversationAgent(BaseMixin, db.Model):
    __tablename__ = 'conversation_agents'
    conversation_id = Column(Integer, ForeignKey('conversations.id'), nullable=False)
    agent_id = Column(Integer, ForeignKey('agents.id'), nullable=False)
    is_default = Column(Boolean, default=False)

    conversation = relationship("Conversation", back_populates="agents")
    agent = relationship("Agent", back_populates="conversation_agents")

    def __repr__(self):
        return f'<ConversationAgent {self.conversation_id}:{self.agent_id}>'

class AutonomousTask(BaseMixin, db.Model):
    __tablename__ = 'autonomous_tasks'
    conversation_id = Column(Integer, ForeignKey('conversations.id'), nullable=False)
    type = Column(String(20), nullable=False)  # discussion, conditional_stop, variable_trigger, time_trigger
    status = Column(String(20), default='active')  # active, completed, stopped
    config = Column(JSON, nullable=False)  # 存储不同类型任务的配置参数

    conversation = relationship("Conversation", back_populates="autonomous_tasks")
    executions = relationship("AutonomousTaskExecution", back_populates="autonomous_task", cascade="all, delete-orphan")

    def __repr__(self):
        return f'<AutonomousTask {self.id}:{self.type}>'

class AutonomousTaskExecution(BaseMixin, db.Model):
    __tablename__ = 'autonomous_task_executions'
    autonomous_task_id = Column(Integer, ForeignKey('autonomous_tasks.id'), nullable=False)
    execution_type = Column(String(20), nullable=False)  # manual, scheduled, triggered
    trigger_source = Column(String(50))  # time, variable_change, condition_met, user
    trigger_data = Column(JSON)  # 触发时的相关数据
    status = Column(String(20), default='running')  # running, completed, failed, stopped
    start_time = Column(DateTime, default=lambda: get_current_time_with_timezone())
    end_time = Column(DateTime)
    result = Column(JSON)  # 执行结果
    error_message = Column(Text)  # 错误信息

    autonomous_task = relationship("AutonomousTask", back_populates="executions")

    def __repr__(self):
        return f'<AutonomousTaskExecution {self.id}:{self.execution_type}>'

class ActionTaskAgent(BaseMixin, db.Model):
    __tablename__ = 'action_task_agents'
    action_task_id = Column(Integer, ForeignKey('action_tasks.id'), nullable=False)
    agent_id = Column(Integer, ForeignKey('agents.id'), nullable=False)
    is_default = Column(Boolean, default=False)

    action_task = relationship("ActionTask", back_populates="agents")
    agent = relationship("Agent", back_populates="action_task_agents")

    def __repr__(self):
        return f'<ActionTaskAgent {self.action_task_id}:{self.agent_id}>'

class ActionTaskEnvironmentVariable(BaseMixin, db.Model):
    __tablename__ = 'action_task_environment_variables'
    name = Column(String(100), nullable=False)
    label = Column(String(100))  # 添加标签字段
    value = Column(Text)
    type = Column(String(20), default='text')  # 只保留text类型
    history = Column(JSON, default=list)

    action_task_id = Column(Integer, ForeignKey('action_tasks.id', ondelete='CASCADE'), nullable=False)
    action_task = relationship("ActionTask", back_populates="environment_variables")

    def __repr__(self):
        return f'<ActionTaskEnvironmentVariable {self.name}>'

class Message(BaseMixin, db.Model):
    __tablename__ = 'messages'
    content = Column(Text, nullable=False)  # 存储所有消息内容，包括思考标签
    # thinking字段已完全弃用，所有处理都移到前端，此字段仅保留数据库兼容性，在代码中不再使用
    thinking = Column(Text)
    raw_message = Column(Text)  # 存储消息的原始内容，包含思考过程等完整内容
    role = Column(String(20), nullable=False)  # human, agent, system, tool, supervisor
    source = Column(String(50), default='taskConversation')  # taskConversation, supervisorConversation
    meta = Column(JSON, default=dict)  # 元数据字段，用于存储额外信息如目标会话类型等

    action_task_id = Column(Integer, ForeignKey('action_tasks.id'), nullable=False)
    conversation_id = Column(Integer, ForeignKey('conversations.id'))
    agent_id = Column(Integer, ForeignKey('agents.id'))
    user_id = Column(Integer, ForeignKey('users.id'))

    action_task = relationship("ActionTask", back_populates="messages")
    conversation = relationship("Conversation", back_populates="messages")
    agent = relationship("Agent", back_populates="messages")

    def __repr__(self):
        return f'<Message {self.id} in action_task {self.action_task_id}>'

class ModelConfig(BaseMixin, db.Model):
    __tablename__ = 'model_configs'
    name = Column(String(100), nullable=False)
    provider = Column(String(50), nullable=False)  # openai, anthropic, etc.
    model_id = Column(String(100), nullable=False)  # gpt-3.5-turbo, claude-3-sonnet, etc.
    base_url = Column(String(255))
    api_key = Column(String(255))
    context_window = Column(Integer, default=65536)  # 上下文窗口大小
    max_output_tokens = Column(Integer, default=2000)  # 最大输出token数
    request_timeout = Column(Integer, default=60)  # 请求超时时间(秒)
    is_default = Column(Boolean, default=False)  # 是否为默认模型（保留向后兼容）
    is_default_text = Column(Boolean, default=False)  # 是否为默认文本生成模型
    is_default_embedding = Column(Boolean, default=False)  # 是否为默认嵌入模型
    is_default_rerank = Column(Boolean, default=False)  # 是否为默认重排序模型
    modalities = Column(JSON, default=list)  # 模型模态，如text_input, text_output, image_input等
    capabilities = Column(JSON, default=list)  # 模型特性标签，如function_calling, reasoning等
    additional_params = Column(JSON, default=dict)  # 额外参数

    def __repr__(self):
        return f'<ModelConfig {self.name}>'

class Tag(BaseMixin, db.Model):
    __tablename__ = 'tags'
    name = Column(String(100), nullable=False)
    type = Column(String(50))  # industry (行业标签), scenario (场景标签)
    description = Column(Text)
    color = Column(String(20))  # 标签颜色代码

    # 关联关系
    action_spaces = relationship("ActionSpaceTag", back_populates="tag")

    def __repr__(self):
        return f'<Tag {self.name}>'

class ActionSpaceTag(BaseMixin, db.Model):
    __tablename__ = 'action_space_tags'
    action_space_id = Column(Integer, ForeignKey('action_spaces.id'), nullable=False)
    tag_id = Column(Integer, ForeignKey('tags.id'), nullable=False)

    # 关联关系
    action_space = relationship("ActionSpace", back_populates="tags")
    tag = relationship("Tag", back_populates="action_spaces")

    def __repr__(self):
        return f'<ActionSpaceTag {self.action_space_id}:{self.tag_id}>'

class SystemSetting(BaseMixin, db.Model):
    """系统设置模型，用于存储全局系统配置"""
    __tablename__ = 'system_settings'
    key = Column(String(100), nullable=False, unique=True)  # 配置键名
    value = Column(Text)  # 配置值
    value_type = Column(String(20), default='string')  # string, number, boolean, json
    description = Column(Text)  # 配置描述
    category = Column(String(50), default='general')  # 配置分类
    is_secret = Column(Boolean, default=False)  # 是否为敏感信息

    def __repr__(self):
        return f'<SystemSetting {self.key}>'

    @classmethod
    def get(cls, key, default=None):
        """获取指定键的配置值"""
        setting = cls.query.filter_by(key=key).first()
        if setting is None:
            return default

        # 根据类型转换值
        if setting.value_type == 'number':
            try:
                if '.' in setting.value:
                    return float(setting.value)
                return int(setting.value)
            except (ValueError, TypeError):
                return default
        elif setting.value_type == 'boolean':
            return setting.value.lower() in ('true', '1', 'yes')
        elif setting.value_type == 'json':
            try:
                import json
                return json.loads(setting.value)
            except:
                return default
        else:
            return setting.value

    @classmethod
    def set(cls, key, value, value_type='string', description=None, category='general', is_secret=False):
        """设置配置键值"""
        # 将值转换为字符串存储
        if value_type == 'json' and not isinstance(value, str):
            import json
            value = json.dumps(value)
        elif not isinstance(value, str):
            value = str(value)

        setting = cls.query.filter_by(key=key).first()
        if setting is None:
            # 创建新配置
            setting = cls(
                key=key,
                value=value,
                value_type=value_type,
                description=description,
                category=category,
                is_secret=is_secret
            )
            from app.extensions import db
            db.session.add(setting)
        else:
            # 更新现有配置
            setting.value = value
            if value_type:
                setting.value_type = value_type
            if description:
                setting.description = description
            if category:
                setting.category = category
            setting.is_secret = is_secret

        # 提交事务
        from app.extensions import db
        db.session.commit()
        return setting

class ActionSpaceRole(BaseMixin, db.Model):
    """行动空间与角色的多对多关联表"""
    __tablename__ = 'action_space_roles'
    action_space_id = Column(Integer, ForeignKey('action_spaces.id'), nullable=False)
    role_id = Column(Integer, ForeignKey('roles.id'), nullable=False)
    quantity = Column(Integer, default=1)  # 角色数量
    settings = Column(JSON, default=dict)  # 角色在该行动空间中的特定设置
    additional_prompt = Column(Text, default='')  # 额外提示词，用于指导角色行为

    action_space = relationship("ActionSpace", back_populates="roles")
    role = relationship("Role", back_populates="action_spaces")

    def __repr__(self):
        return f'<ActionSpaceRole {self.action_space_id}:{self.role_id}>'

class ActionSpaceEnvironmentVariable(db.Model):
    """行动空间环境变量模型

    用于存储行动空间级别的环境变量配置
    """
    __tablename__ = 'action_space_environment_variables'

    id = db.Column(db.Integer, primary_key=True)
    action_space_id = db.Column(db.Integer, db.ForeignKey('action_spaces.id'), nullable=False)
    name = db.Column(db.String(100), nullable=False)
    label = db.Column(db.String(100), nullable=False)
    type = db.Column(db.String(20), nullable=False, default='text')  # 只保留text类型
    default_value = db.Column(db.String(500), nullable=False)
    description = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 关联关系
    action_space = db.relationship('ActionSpace', backref=db.backref('environment_variables', lazy=True))

    def __repr__(self):
        return f'<ActionSpaceEnvironmentVariable {self.name}>'

class RoleVariable(BaseMixin, db.Model):
    """角色变量模型

    用于存储角色级别的变量配置，这些变量会在创建行动任务时被实例化为智能体变量
    每个角色变量都与特定行动空间绑定，不影响其他行动空间
    """
    __tablename__ = 'role_variables'

    role_id = db.Column(db.Integer, db.ForeignKey('roles.id'), nullable=False)
    action_space_id = db.Column(db.Integer, db.ForeignKey('action_spaces.id'), nullable=False)
    name = db.Column(db.String(100), nullable=False)
    label = db.Column(db.String(100), nullable=False)
    type = db.Column(db.String(20), nullable=False, default='text')  # 只保留text类型
    default_value = db.Column(db.String(500), nullable=False)
    description = db.Column(db.Text)

    # 关联关系
    role = db.relationship('Role', backref=db.backref('variables', lazy=True))
    action_space = db.relationship('ActionSpace')

    def __repr__(self):
        return f'<RoleVariable {self.name} for role {self.role_id}>'

class ExternalEnvironmentVariable(db.Model):
    """外部环境变量模型

    用于存储通过REST API定期同步获取的外部环境变量
    """
    __tablename__ = 'external_environment_variables'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    label = db.Column(db.String(200), nullable=False)
    api_url = db.Column(db.String(500), nullable=False)
    api_method = db.Column(db.String(10), nullable=False, default='GET')
    sync_interval = db.Column(db.Integer, nullable=False, default=300)  # 秒
    sync_enabled = db.Column(db.Boolean, nullable=False, default=True)
    current_value = db.Column(db.Text)
    last_sync = db.Column(db.DateTime)
    last_error = db.Column(db.Text)
    status = db.Column(db.String(20), nullable=False, default='inactive')  # active, error, inactive
    settings = db.Column(db.JSON)  # 存储扩展配置：api_headers, data_path, data_type, timeout, description等
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def to_dict(self):
        result = {
            'id': self.id,
            'name': self.name,
            'label': self.label,
            'api_url': self.api_url,
            'api_method': self.api_method,
            'sync_interval': self.sync_interval,
            'sync_enabled': self.sync_enabled,
            'current_value': self.current_value,
            'last_sync': self.last_sync.isoformat() if self.last_sync else None,
            'last_error': self.last_error,
            'status': self.status,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

        # 合并settings中的字段
        if self.settings:
            result.update({
                'api_headers': self.settings.get('api_headers', '{}'),
                'data_path': self.settings.get('data_path', ''),
                'data_type': self.settings.get('data_type', 'string'),
                'timeout': self.settings.get('timeout', 10),
                'description': self.settings.get('description', '')
            })
        else:
            result.update({
                'api_headers': '{}',
                'data_path': '',
                'data_type': 'string',
                'timeout': 10,
                'description': ''
            })

        return result

    def __repr__(self):
        return f'<ExternalEnvironmentVariable {self.name}>'

# 添加行动空间与规则集的多对多关联表
class ActionSpaceRuleSet(BaseMixin, db.Model):
    """行动空间与规则集的多对多关联表"""
    __tablename__ = 'action_space_rule_sets'
    action_space_id = Column(Integer, ForeignKey('action_spaces.id'), nullable=False)
    rule_set_id = Column(Integer, ForeignKey('rule_sets.id'), nullable=False)
    settings = Column(JSON, default=dict)  # 关联特定设置

    action_space = relationship("ActionSpace", back_populates="rule_sets")
    rule_set = relationship("RuleSet", back_populates="action_spaces")

    def __repr__(self):
        return f'<ActionSpaceRuleSet {self.action_space_id}:{self.rule_set_id}>'

class ActionSpaceObserver(BaseMixin, db.Model):
    """行动空间与监督者角色的多对多关联表"""
    __tablename__ = 'action_space_observers'
    action_space_id = Column(Integer, ForeignKey('action_spaces.id'), nullable=False)
    role_id = Column(Integer, ForeignKey('roles.id'), nullable=False)
    settings = Column(JSON, default=dict)  # 监督者在该行动空间中的特定设置
    additional_prompt = Column(Text, default='')  # 额外提示词，用于指导监督者行为
    # 移除规则集关联，规则集应该与行动空间关联，而不是与单个监督者关联

    action_space = relationship("ActionSpace", back_populates="observers")
    role = relationship("Role")

    def __repr__(self):
        return f'<ActionSpaceObserver {self.action_space_id}:{self.role_id}>'

    def get_default_supervision_settings(self):
        """获取默认的监督设置"""
        return {
            "supervision_mode": "round_based",  # immediate, round_based, variable_based
            "triggers": {
                "after_each_agent": False,
                "after_each_round": True,
                "on_rule_violation": True
            },
            "variable_conditions": [],  # 变量监督条件
            "condition_logic": "and",  # 条件逻辑：and/or
            "check_interval": 60,  # 变量检查间隔（秒）
            "intervention_settings": {
                "threshold": 0.7,  # 监督者介入的置信度阈值
                "max_interventions_per_round": 2,  # 每轮最大干预次数
                "intervention_mode": "basic"  # 干预模式：basic, suggestion, active
            },
            "monitoring_scope": {
                "rule_compliance": True,  # 监控规则遵守情况（始终启用）
                "conversation_quality": False,  # 监控对话质量（待实现功能，暂时关闭）
                "task_progress": False,  # 监控任务进度（待实现功能，暂时关闭）
                "agent_behavior": False  # 监控智能体行为（待实现功能，暂时关闭）
            },
            "reporting": {
                "generate_summary": True,  # 生成监督总结
                "log_interventions": True,  # 记录干预日志
                "alert_on_issues": True  # 发现问题时发出警报
            }
        }

    def get_supervision_settings(self):
        """获取监督设置，如果没有则返回默认设置"""
        if not self.settings:
            return self.get_default_supervision_settings()

        # 合并默认设置和用户设置
        default_settings = self.get_default_supervision_settings()
        user_settings = self.settings.get('supervision', {})

        # 深度合并设置
        def deep_merge(default, user):
            result = default.copy()
            for key, value in user.items():
                if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                    result[key] = deep_merge(result[key], value)
                else:
                    result[key] = value
            return result

        return deep_merge(default_settings, user_settings)