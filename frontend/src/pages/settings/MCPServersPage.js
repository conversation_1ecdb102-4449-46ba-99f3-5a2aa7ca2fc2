import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  Button, Table, Space, Modal, Form, Input,
  Typography, Tag, Switch, Collapse,
  Input as AntInput, Spin, List, Descriptions, Radio, App
} from 'antd';
import {
  PlusOutlined, DeleteOutlined, EditOutlined,
  PlayCircleOutlined, PauseCircleOutlined, CodeOutlined,
  SaveOutlined, ToolOutlined,
  FormatPainterOutlined, CopyOutlined,
  SyncOutlined, CheckCircleOutlined, CloseCircleOutlined
} from '@ant-design/icons';
import axios from 'axios';
import Editor from '@monaco-editor/react';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = AntInput;



// 获取API基础URL的帮助函数
const getApiBaseUrl = () => {
  return process.env.REACT_APP_API_URL || 'http://localhost:8080';
};

const MCPServersPage = () => {
  const { message, modal } = App.useApp();
  const [servers, setServers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingServer, setEditingServer] = useState(null);
  const [form] = Form.useForm();
  const [configVisible, setConfigVisible] = useState(false);
  const [configContent, setConfigContent] = useState('');
  const [savingConfig, setSavingConfig] = useState(false);
  const [expandedRowKeys, setExpandedRowKeys] = useState([]);
  const [serverTools, setServerTools] = useState({});
  const [loadingTools, setLoadingTools] = useState({});
  const [currentCommType, setCurrentCommType] = useState('stdio'); // 添加状态来跟踪当前通信方式

  // 获取MCP服务器工具列表
  const fetchServerTools = useCallback(async (serverId, refresh = false) => {
    setLoadingTools(prev => ({ ...prev, [serverId]: true }));
    try {
      // 使用环境变量中的API URL
      const apiUrl = `${getApiBaseUrl()}/mcp/tools/${serverId}`;

      console.log(`正在获取服务器 ${serverId} 的工具列表${refresh ? '(强制刷新)' : ''}:`, apiUrl);
      // 添加refresh参数，指示后端是否强制刷新缓存
      const response = await axios.post(apiUrl, { refresh });

      // 设置工具列表 - 处理MCP标准格式
      if (response.data) {
        // 如果响应是数组，则直接作为工具列表使用
        if (Array.isArray(response.data)) {
          setServerTools(prev => ({
            ...prev,
            [serverId]: response.data
          }));
          console.log(`获取到服务器 ${serverId} 的工具列表:`, response.data);
        }
        // 如果响应含有tools属性，使用旧格式处理
        else if (response.data.tools) {
          setServerTools(prev => ({
            ...prev,
            [serverId]: response.data.tools
          }));
          console.log(`获取到服务器 ${serverId} 的工具列表:`, response.data.tools);
        }
        else {
          console.warn(`获取到的服务器 ${serverId} 的工具列表格式不支持:`, response.data);
          message.warning(`服务器 ${serverId} 的工具列表格式不支持`);
        }
      } else {
        console.warn(`获取到的服务器 ${serverId} 的工具列表为空`);
        message.warning(`服务器 ${serverId} 的工具列表为空`);
      }
    } catch (error) {
      console.error(`获取服务器 ${serverId} 的工具列表失败:`, error);
      message.error(`获取服务器 ${serverId} 的工具列表失败: ${error.message}`);
    } finally {
      setLoadingTools(prev => ({ ...prev, [serverId]: false }));
    }
  }, [message, setLoadingTools, setServerTools]);

  // 获取服务器列表
  const fetchServers = useCallback(async (refreshTools = true) => {
    setLoading(true);
    try {
      // 使用环境变量中的API URL
      const apiUrl = `${getApiBaseUrl()}/mcp/servers`;

      console.log('正在请求MCP服务器列表:', apiUrl);
      const response = await axios.get(apiUrl);
      setServers(response.data);
      console.log('获取到MCP服务器列表:', response.data);

      // 只有当明确要求刷新工具列表时才刷新
      // 这样在展开/收起工具时就不会触发不必要的刷新
      if (refreshTools && expandedRowKeys.length > 0) {
        expandedRowKeys.forEach(serverId => {
          fetchServerTools(serverId, false); // 使用false参数，避免强制刷新
        });
      }
    } catch (error) {
      console.error('获取MCP服务器列表失败:', error);
      console.error('错误详情:', error.response || error.message);
      message.error(`获取MCP服务器列表失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
  }, [fetchServerTools, message, setLoading, setServers, expandedRowKeys]);

  // 使用useRef保存fetchServers函数的引用，避免依赖项问题
  const fetchServersRef = useRef(null);
  useEffect(() => {
    // 更新ref中的函数引用
    fetchServersRef.current = fetchServers;
  }, [fetchServers]);

  // 只在组件挂载时获取服务器列表
  useEffect(() => {
    // 使用ref中的函数，避免依赖于fetchServers
    if (fetchServersRef.current) {
      fetchServersRef.current(true); // 初始加载时刷新工具列表
    }
  }, []);

  // 启用服务器
  const enableServer = async (serverId) => {
    try {
      const apiUrl = `${getApiBaseUrl()}/mcp/servers/${serverId}/enable`;

      const response = await axios.post(apiUrl);

      // 检查API返回的状态
      if (response.data.status === "error") {
        // 如果返回错误状态，显示错误消息
        console.error(`启用服务器失败:`, response.data.message);
        message.error(response.data.message || `启动服务器失败，未标记为启用`);
      } else {
        // 如果成功，显示成功消息
        message.success(response.data.message || `服务器 ${serverId} 已启用并成功启动`);
      }

      // 无论成功还是失败，都刷新服务器列表，但不自动刷新工具列表
      await fetchServers(false);

      // 如果当前服务器已展开，则手动刷新工具列表
      if (expandedRowKeys.includes(serverId)) {
        fetchServerTools(serverId);
      }
    } catch (error) {
      console.error(`启用服务器请求失败:`, error);
      message.error(`启用服务器 ${serverId} 请求失败: ${error.message}`);
    }
  };

  // 禁用服务器
  const disableServer = async (serverId) => {
    try {
      const apiUrl = `${getApiBaseUrl()}/mcp/servers/${serverId}/disable`;

      const response = await axios.post(apiUrl);

      // 检查API返回的状态
      if (response.data.status === "error") {
        // 如果返回错误状态，显示错误消息
        console.error(`禁用服务器失败:`, response.data.message);
        message.warning(response.data.message || `服务器 ${serverId} 已标记为禁用，但停止失败`);
      } else {
        // 如果成功，显示成功消息
        message.success(response.data.message || `服务器 ${serverId} 已禁用`);
      }

      // 无论成功还是失败，都刷新服务器列表，但不自动刷新工具列表
      await fetchServers(false);

      // 如果当前服务器已展开，则手动刷新工具列表
      if (expandedRowKeys.includes(serverId)) {
        fetchServerTools(serverId);
      }
    } catch (error) {
      console.error(`禁用服务器请求失败:`, error);
      message.error(`禁用服务器 ${serverId} 请求失败: ${error.message}`);
    }
  };

  // 删除服务器
  const deleteServer = async (serverId) => {
    modal.confirm({
      title: '确认删除',
      content: `确定要删除服务器 ${serverId} 吗？内部服务器无法删除。`,
      onOk: async () => {
        try {
          const apiUrl = `${getApiBaseUrl()}/mcp/servers/${serverId}`;

          await axios.delete(apiUrl);
          message.success(`服务器 ${serverId} 已删除`);
          fetchServers(false); // 刷新服务器列表，但不刷新工具列表
        } catch (error) {
          console.error(`删除服务器失败:`, error);
          message.error(`删除服务器 ${serverId} 失败: ${error.response?.data?.message || error.message}`);
        }
      }
    });
  };

  // 打开编辑模态框
  const openEditModal = (server = null) => {
    setEditingServer(server);
    if (server) {
      // 将环境变量对象转换为JSON字符串
      const envString = server.env ? JSON.stringify(server.env, null, 2) : '{}';
      const commType = server.comm_type || 'stdio';

      // 设置当前通信方式状态
      setCurrentCommType(commType);

      // 设置表单初始值
      form.setFieldsValue({
        id: server.id,
        description: server.description,
        internal: server.internal,
        comm_type: commType,
        env: envString
      });

      // 根据通信方式设置不同字段
      if (commType === 'stdio') {
        // 安全处理args字段，支持数组、字符串或空值
        let argsString = '';
        if (server.args) {
          if (Array.isArray(server.args)) {
            argsString = server.args.join(' ');
          } else if (typeof server.args === 'string') {
            argsString = server.args;
          }
        }

        form.setFieldsValue({
          command: server.command,
          args: argsString
        });
      } else if (commType === 'http' || commType === 'sse') {
        form.setFieldsValue({
          url: server.url
        });
      }
    } else {
      // 设置默认通信方式状态
      setCurrentCommType('stdio');

      form.resetFields();
      // 确保新建服务器时internal默认为false，并设置空环境变量
      form.setFieldsValue({
        internal: false,
        comm_type: 'stdio',
        env: '{}'
      });
    }
    setModalVisible(true);
  };

  // 保存服务器
  const saveServer = async () => {
    try {
      // 根据当前通信方式确定需要验证的字段
      const commType = form.getFieldValue('comm_type');
      let fieldsToValidate = ['id', 'description', 'comm_type'];

      // 根据通信方式添加需要验证的字段
      if (commType === 'stdio') {
        fieldsToValidate.push('command');
        // args字段改为非必填，不添加到验证列表中
      } else if (commType === 'http' || commType === 'sse') {
        fieldsToValidate.push('url');
      }

      // 只验证相关字段
      const values = await form.validateFields(fieldsToValidate);

      // 获取所有表单值，包括未验证的字段
      const allValues = form.getFieldsValue();

      const config = {
        description: values.description,
        internal: false, // 强制设置为false，不允许创建内部服务器
        comm_type: values.comm_type || 'stdio',
        enabled: false  // 默认为禁用状态，需要用户手动启用
      };

      // 根据通信方式设置不同的配置项
      if (values.comm_type === 'stdio') {
        // 标准输入输出通信方式需要command和args
        config.command = values.command;
        // 安全处理args字段，允许为空，使用allValues获取args值
        if (allValues.args && allValues.args.trim()) {
          config.args = allValues.args.split(' ').filter(arg => arg.trim() !== '');
        } else {
          config.args = [];
        }
      } else if (values.comm_type === 'http' || values.comm_type === 'sse') {
        // HTTP/SSE通信方式需要url
        config.url = values.url;
      }

      // 解析并添加环境变量，使用allValues获取env值
      try {
        if (allValues.env) {
          const envObj = JSON.parse(allValues.env);
          if (typeof envObj === 'object' && envObj !== null) {
            config.env = envObj;
          }
        }
      } catch (e) {
        message.warning('环境变量JSON格式不正确，将使用空环境变量');
        config.env = {};
      }

      if (editingServer) {
        // 更新服务器时，保留原来的internal值和enabled值
        config.internal = editingServer.internal;

        // 如果是内部服务器，则始终为启用状态
        if (editingServer.internal) {
          config.enabled = true;
        } else {
          // 保留原来的启用状态
          config.enabled = editingServer.enabled !== undefined ? editingServer.enabled :
                          (editingServer.status === 'running');
        }

        // 更新服务器
        const apiUrl = `${getApiBaseUrl()}/mcp/servers/${values.id}`;

        await axios.put(apiUrl, config);
        message.success(`服务器 ${values.id} 已更新`);
      } else {
        // 添加服务器
        const apiUrl = `${getApiBaseUrl()}/mcp/servers`;

        await axios.post(apiUrl, { id: values.id, config });
        message.success(`服务器 ${values.id} 已添加`);
      }

      setModalVisible(false);
      fetchServers(false); // 刷新服务器列表，但不刷新工具列表
    } catch (error) {
      if (error.errorFields) {
        message.error('请完成必填字段');
      } else {
        console.error('保存服务器失败:', error);
        message.error(`保存服务器失败: ${error.response?.data?.message || error.message}`);
      }
    }
  };

  // 打开配置编辑模态框
  const openConfigEditor = async () => {
    try {
      // 获取完整的MCP配置文件内容
      const apiUrl = `${getApiBaseUrl()}/mcp/servers/config`;

      console.log('正在获取MCP配置文件:', apiUrl);
      const response = await axios.get(apiUrl);

      if (response.data) {
        // 使用后端返回的完整配置
        setConfigContent(JSON.stringify(response.data, null, 2));
        console.log('成功获取MCP配置文件');
      } else {
        // 如果后端没有返回配置，使用服务器列表生成临时配置
        const config = {
          mcpServers: {}
        };

        servers.forEach(server => {
          config.mcpServers[server.id] = {
            command: server.command,
            args: server.args,
            description: server.description,
            internal: server.internal,
            comm_type: server.comm_type || 'stdio'
          };

          // 如果是http通信方式且有URL，则添加URL
          if (server.comm_type === 'http' && server.url) {
            config.mcpServers[server.id].url = server.url;
          }

          // 如果有环境变量，则添加到配置中
          if (server.env) {
            config.mcpServers[server.id].env = server.env;
          }
        });

        setConfigContent(JSON.stringify(config, null, 2));
        console.warn('后端未返回配置，使用临时配置');
      }

      setConfigVisible(true);
    } catch (error) {
      console.error('获取MCP配置失败:', error);
      message.error(`获取MCP配置失败: ${error.message}`);

      // 发生错误时，仍然打开模态框并使用临时配置
      const config = {
        mcpServers: {}
      };

      servers.forEach(server => {
        config.mcpServers[server.id] = {
          command: server.command,
          args: server.args,
          description: server.description,
          internal: server.internal,
          comm_type: server.comm_type || 'stdio'
        };

        // 如果是http通信方式且有URL，则添加URL
        if (server.comm_type === 'http' && server.url) {
          config.mcpServers[server.id].url = server.url;
        }

        // 如果有环境变量，则添加到配置中
        if (server.env) {
          config.mcpServers[server.id].env = server.env;
        }
      });

      setConfigContent(JSON.stringify(config, null, 2));
      setConfigVisible(true);
    }
  };

  // 保存MCP配置
  const saveConfig = async () => {
    try {
      setSavingConfig(true);

      // 验证JSON格式
      let configObj;
      try {
        configObj = JSON.parse(configContent);
      } catch (e) {
        message.error('配置格式不正确，请检查JSON语法');
        return;
      }

      // 验证配置结构
      if (!configObj.mcpServers) {
        message.error('配置结构不正确，必须包含mcpServers对象');
        return;
      }

      // 确保所有服务器都有enabled属性，内部服务器始终为启用状态
      Object.keys(configObj.mcpServers).forEach(serverId => {
        const serverConfig = configObj.mcpServers[serverId];

        // 移除auto_start属性
        if ('auto_start' in serverConfig) {
          delete serverConfig.auto_start;
        }

        // 确保内部服务器始终为启用状态
        if (serverConfig.internal) {
          serverConfig.enabled = true;
        } else if (!('enabled' in serverConfig)) {
          // 如果没有enabled属性，默认为禁用状态
          serverConfig.enabled = false;
        }
      });

      // 保存配置文件
      const apiUrl = `${getApiBaseUrl()}/mcp/servers/config`;

      await axios.post(apiUrl, { config: configObj });
      message.success('MCP配置已保存并生效');
      setConfigVisible(false);

      // 重新加载服务器列表，但不刷新工具列表
      fetchServers(false);
    } catch (error) {
      console.error('保存MCP配置失败:', error);
      message.error(`保存MCP配置失败: ${error.response?.data?.message || error.message}`);
    } finally {
      setSavingConfig(false);
    }
  };

  // 格式化JSON配置
  const formatConfig = () => {
    try {
      const configObj = JSON.parse(configContent);
      const formatted = JSON.stringify(configObj, null, 2);
      setConfigContent(formatted);
      message.success('配置已格式化');
    } catch (e) {
      message.error('配置格式不正确，无法格式化');
    }
  };

  // 复制配置到剪贴板
  const copyConfig = () => {
    navigator.clipboard.writeText(configContent)
      .then(() => {
        message.success('配置已复制到剪贴板');
      })
      .catch(() => {
        message.error('复制失败，请手动复制');
      });
  };

  // 展开行处理
  const handleExpand = (expanded, record) => {
    if (expanded) {
      // 当展开一行时，加载该服务器的工具列表，但不强制刷新
      // 注意：这里只刷新工具列表，不刷新服务器列表
      fetchServerTools(record.id, false);
      setExpandedRowKeys([record.id]);
    } else {
      // 收起行时，只清空展开状态，不触发任何刷新
      setExpandedRowKeys([]);
    }
  };

  // 渲染工具参数
  const renderToolParams = (schema) => {
    if (!schema || !schema.properties) {
      return <Text type="secondary">无参数</Text>;
    }

    // 从inputSchema的properties中提取参数
    const paramsList = Object.entries(schema.properties).map(([name, prop]) => ({
      name,
      type: Array.isArray(prop.type) ? prop.type.join(' | ') : prop.type,
      description: prop.description || '',
      required: schema.required && schema.required.includes(name)
    }));

    if (paramsList.length === 0) {
      return <Text type="secondary">无参数</Text>;
    }

    return (
      <List
        size="small"
        dataSource={paramsList}
        renderItem={param => (
          <List.Item>
            <Text strong>{param.name}</Text>{param.required && <Tag color="red" style={{marginLeft: 5}}>必填</Tag>}: {param.type}
            {param.description && <div><Text type="secondary">{param.description}</Text></div>}
          </List.Item>
        )}
      />
    );
  };

  // 添加监听函数，用于处理通信方式变更
  const handleCommTypeChange = (e) => {
    const commType = e.target.value;
    setCurrentCommType(commType); // 更新状态

    // 根据通信方式更新字段验证规则
    if (commType === 'stdio') {
      // 当选择stdio时，只验证stdio相关字段
      form.validateFields(['command', 'args']);
    } else if (commType === 'http' || commType === 'sse') {
      // 当选择http/sse时，只验证url相关字段
      form.validateFields(['url']);
    }
  };

  // 表格列定义
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 150,
    },
    {
      title: '工具',
      key: 'tools',
      width: 100,
      render: (_, record) => (
        <Button
          type="text"
          icon={<ToolOutlined />}
          size="small"
          onClick={(e) => {
            // 阻止事件冒泡，避免触发行点击
            e.stopPropagation();
            // 判断当前行是否已展开
            const isExpanded = expandedRowKeys.includes(record.id);
            if (isExpanded) {
              // 如果已展开则收起，不触发任何刷新
              setExpandedRowKeys([]);
            } else {
              // 如果未展开则展开并加载工具，并强制刷新
              // 注意：这里只刷新工具列表，不刷新服务器列表
              fetchServerTools(record.id, true);
              setExpandedRowKeys([record.id]);
            }
          }}
        >
          {expandedRowKeys.includes(record.id) ? '收起' : '刷新工具'}
        </Button>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status, record) => {
        // 优先使用enabled字段，如果不存在则使用status字段
        const isEnabled = record.enabled !== undefined ? record.enabled : (status === 'running');
        return isEnabled ?
          <Tag color="green">已启用</Tag> :
          <Tag color="red">已禁用</Tag>;
      }
    },
    {
      title: '运行状态',
      dataIndex: 'running',
      key: 'running',
      width: 100,
      render: (running, record) => {
        // 使用running字段显示运行状态，不再回退到status字段
        // 因为status字段表示的是启用状态，而不是运行状态
        const isRunning = running === true;
        return isRunning ?
          <Tag icon={<CheckCircleOutlined />} color="green">运行中</Tag> :
          <Tag icon={<CloseCircleOutlined />} color="red">已停止</Tag>;
      }
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: '类型',
      dataIndex: 'internal',
      key: 'internal',
      width: 100,
      render: internal => internal ?
        <Tag color="blue">内部</Tag> :
        <Tag color="orange">外部</Tag>
    },
    {
      title: '通信方式',
      dataIndex: 'comm_type',
      key: 'comm_type',
      width: 120,
      render: commType => {
        if (commType === 'stdio') {
          return <Tag color="purple">标准 I/O</Tag>;
        } else if (commType === 'http') {
          return <Tag color="cyan">HTTP</Tag>;
        } else if (commType === 'sse') {
          return <Tag color="orange">SSE</Tag>;
        } else {
          return <Tag color="default">{commType || '未知'}</Tag>;
        }
      }
    },

    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record) => (
        <Space size="small">
          {/* 优先使用enabled字段，如果不存在则使用status字段 */}
          {(!record.enabled && record.enabled !== undefined) || (record.enabled === undefined && record.status !== 'running') ? (
            !record.internal ? (
              <Button
                type="text"
                icon={<PlayCircleOutlined />}
                onClick={() => enableServer(record.id)}
              >
                启用
              </Button>
            ) : null
          ) : (
            record.internal ? null : (
              <Button
                type="text"
                danger
                icon={<PauseCircleOutlined />}
                onClick={() => disableServer(record.id)}
              >
                禁用
              </Button>
            )
          )}

          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => openEditModal(record)}
            style={{ color: '#1677ff' }}
          >
            编辑
          </Button>

          {!record.internal && (
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              onClick={() => deleteServer(record.id)}
            >
              删除
            </Button>
          )}
        </Space>
      ),
    },
  ];

  // 展开行内容渲染
  const expandedRowRender = (record) => {
    const serverId = record.id;
    const isLoading = loadingTools[serverId];
    const tools = serverTools[serverId] || [];

    if (isLoading) {
      return (
        <div style={{ textAlign: 'center', padding: 8 }}>
          <Spin size="small" />
          <div style={{ marginTop: 4 }}>正在加载工具列表...</div>
        </div>
      );
    }

    if (tools.length === 0) {
      return (
        <div style={{ padding: 8 }}>
          <Text type="secondary">该服务器没有提供工具或无法获取工具列表</Text>
        </div>
      );
    }

    return (
      <div style={{ padding: '0' }}>
        <div style={{ display: 'flex', alignItems: 'center', margin: '8px 0' }}>
          <ToolOutlined style={{ marginRight: 8 }} />
          <Text strong>可用工具列表 ({tools.length})</Text>
        </div>

        <Collapse
          style={{ marginBottom: 8 }}
          items={tools.map(tool => ({
            key: tool.name,
            label: (
              <Space>
                <Text strong>{tool.name}</Text>
                <Tag color="blue">工具</Tag>
                {tool.annotations?.title && <Tag color="purple">{tool.annotations.title}</Tag>}
                {tool.annotations?.readOnlyHint && <Tag color="green">只读</Tag>}
                {tool.annotations?.destructiveHint && <Tag color="red">破坏性</Tag>}
              </Space>
            ),
            children: (
              <Descriptions column={1} bordered size="small">
                <Descriptions.Item label="描述">
                  {tool.description || '无描述'}
                </Descriptions.Item>
                <Descriptions.Item label="参数">
                  {renderToolParams(tool.inputSchema)}
                </Descriptions.Item>
                {tool.annotations && (
                  <Descriptions.Item label="注解">
                    <div>
                      {tool.annotations.readOnlyHint !== undefined &&
                        <div>只读操作: {tool.annotations.readOnlyHint ? '是' : '否'}</div>}
                      {tool.annotations.destructiveHint !== undefined &&
                        <div>破坏性操作: {tool.annotations.destructiveHint ? '是' : '否'}</div>}
                      {tool.annotations.idempotentHint !== undefined &&
                        <div>幂等操作: {tool.annotations.idempotentHint ? '是' : '否'}</div>}
                      {tool.annotations.openWorldHint !== undefined &&
                        <div>开放世界: {tool.annotations.openWorldHint ? '是' : '否'}</div>}
                    </div>
                  </Descriptions.Item>
                )}
              </Descriptions>
            )
          }))}
        />
      </div>
    );
  };

  return (
    <div>
      <div style={{ marginBottom: '24px' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
          <div>
            <Title level={4} style={{ margin: 0, marginBottom: '8px' }}>MCP 服务器管理</Title>
            <Text type="secondary">
              Model Context Protocol (MCP) 服务器允许AI智能体通过工具调用与外部系统交互
            </Text>
          </div>
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => openEditModal()}
              size="large"
              style={{
                borderRadius: '8px',
                height: '42px',
                fontSize: '14px'
              }}
            >
              添加服务器
            </Button>

            <Button
              type="default"
              icon={<CodeOutlined />}
              onClick={openConfigEditor}
              size="large"
              style={{
                borderRadius: '8px',
                height: '42px',
                fontSize: '14px'
              }}
            >
              编辑配置文件
            </Button>

            <Button
              type="default"
              icon={<SyncOutlined />}
              onClick={() => fetchServers(true)} // 明确传递true参数，表示需要刷新工具列表
              size="large"
              style={{
                borderRadius: '8px',
                height: '42px',
              }}
            >
              刷新
            </Button>
          </Space>
        </div>
      </div>

      <Table
        columns={columns}
        dataSource={servers}
        rowKey="id"
        loading={loading}
        pagination={false}
        expandable={{
          expandedRowRender,
          expandedRowKeys,
          onExpand: handleExpand,
          expandRowByClick: false
        }}
      />

      {/* 添加/编辑服务器模态框 */}
      <Modal
        title={editingServer ? '编辑MCP服务器' : '添加MCP服务器'}
        open={modalVisible}
        onOk={saveServer}
        onCancel={() => setModalVisible(false)}
        width={700}
        destroyOnHidden
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{ internal: false, comm_type: 'stdio' }}
        >
          <Form.Item
            name="id"
            label="服务器ID"
            rules={[{ required: true, message: '请输入服务器ID' }]}
            extra="唯一标识符，如 playwright, searxng"
          >
            <Input placeholder="例如: playwright" disabled={!!editingServer} />
          </Form.Item>

          <Form.Item
            name="comm_type"
            label="通信方式"
            extra="选择与MCP服务器的通信方式"
          >
            <Radio.Group onChange={handleCommTypeChange}>
              <Radio value="stdio">标准输入输出 (stdio)</Radio>
              <Radio value="http">HTTP</Radio>
              <Radio value="sse">Server-Sent Events (SSE)</Radio>
            </Radio.Group>
          </Form.Item>

          {/* 当通信方式为stdio时显示的字段 */}
          <div className="stdio-fields" style={{ display: currentCommType === 'stdio' ? 'block' : 'none' }}>
            <Form.Item
              name="command"
              label="命令"
              rules={[{
                required: currentCommType === 'stdio',
                message: '请输入命令',
                validator: (_, value) => {
                  // 只在stdio模式下验证
                  if (currentCommType !== 'stdio') {
                    return Promise.resolve();
                  }
                  if (!value && currentCommType === 'stdio') {
                    return Promise.reject('请输入命令');
                  }
                  return Promise.resolve();
                }
              }]}
              extra="执行MCP服务器的命令，如 npx, curl"
            >
              <Input placeholder="例如: npx" />
            </Form.Item>

            <Form.Item
              name="args"
              label="参数"
              extra="命令参数，用空格分隔（可选）"
            >
              <Input placeholder="例如: @playwright/mcp@latest --vision" />
            </Form.Item>
          </div>

          {/* 当通信方式为http或sse时显示的字段 */}
          <Form.Item
            name="url"
            label={currentCommType === 'sse' ? 'SSE URL' : 'HTTP URL'}
            rules={[{
              required: currentCommType === 'http' || currentCommType === 'sse',
              message: '请输入URL',
              validator: (_, value) => {
                // 只在http/sse模式下验证
                if (currentCommType !== 'http' && currentCommType !== 'sse') {
                  return Promise.resolve();
                }
                if (!value && (currentCommType === 'http' || currentCommType === 'sse')) {
                  return Promise.reject('请输入URL');
                }
                return Promise.resolve();
              }
            }]}
            extra={currentCommType === 'sse'
              ? "SSE端点URL (如 http://localhost:8000/sse)"
              : "MCP服务器URL (可以是MCP标准API或OpenAPI规范URL，如http://example.com/openapi.json)"
            }
            className="http-fields"
            style={{ display: (currentCommType === 'http' || currentCommType === 'sse') ? 'block' : 'none' }}
          >
            <Input placeholder={currentCommType === 'sse'
              ? "例如: http://localhost:8000/sse"
              : "例如: http://localhost:3000 或 http://*********:8888/openapi.json"
            } />
          </Form.Item>

          <Form.Item
            name="description"
            label="描述"
            extra="服务器功能描述"
          >
            <Input placeholder="例如: Playwright浏览器自动化服务器" />
          </Form.Item>

          <Form.Item
            name="env"
            label="环境变量"
            extra="JSON格式的环境变量，例如：{'KEY1': 'value1', 'KEY2': 'value2'}"
          >
            <TextArea
              rows={4}
              placeholder='{"KEY1": "value1", "KEY2": "value2"}'
              style={{ fontFamily: 'monospace' }}
            />
          </Form.Item>

          <Form.Item
            name="internal"
            label="内部服务器"
            valuePropName="checked"
            extra="内部服务器由应用内部提供，不需要单独启动"
          >
            <Switch disabled={true} />
          </Form.Item>

        </Form>
      </Modal>

      {/* 配置文件编辑模态框 */}
      <Modal
        title="编辑MCP配置文件"
        open={configVisible}
        onCancel={() => setConfigVisible(false)}
        footer={[
          <Button key="cancel" onClick={() => setConfigVisible(false)}>
            取消
          </Button>,
          <Button
            key="format"
            icon={<FormatPainterOutlined />}
            onClick={formatConfig}
          >
            格式化
          </Button>,
          <Button
            key="copy"
            icon={<CopyOutlined />}
            onClick={copyConfig}
          >
            复制
          </Button>,
          <Button
            key="save"
            type="primary"
            icon={<SaveOutlined />}
            loading={savingConfig}
            onClick={saveConfig}
          >
            保存并应用
          </Button>
        ]}
        width={900}
        styles={{ body: { maxHeight: '80vh', overflow: 'auto' } }}
      >
        <Paragraph>
          您可以直接编辑MCP配置文件，保存后将立即生效。修改配置会影响MCP服务器的注册和运行状态。
        </Paragraph>

        <Collapse
          defaultActiveKey={['1']}
          items={[
            {
              key: '1',
              label: '配置文件使用说明',
              children: (
                <>
                  <Paragraph>
                    <Text strong>配置格式说明:</Text>
                  </Paragraph>
                  <Paragraph>
                    <ul>
                      <li>配置必须是有效的JSON格式</li>
                      <li>根对象必须包含 <Text code>mcpServers</Text> 属性</li>
                      <li>每个服务器必须指定 <Text code>command</Text> 和 <Text code>args</Text> 属性（使用stdio通信方式时）</li>
                      <li>内部服务器(如variables-server)请保留 <Text code>internal</Text> 属性为 <Text code>true</Text></li>
                      <li>通信方式 <Text code>comm_type</Text> 可以是 <Text code>stdio</Text>(默认)、<Text code>http</Text> 或 <Text code>sse</Text></li>
                      <li>使用 <Text code>http</Text> 或 <Text code>sse</Text> 通信时需要提供 <Text code>url</Text> 属性</li>
                      <li>支持OpenAPI规范URL（如<Text code>http://example.com/openapi.json</Text>），将自动转换为MCP工具</li>
                      <li><Text code>sse</Text> 类型专门用于Server-Sent Events连接（如<Text code>http://localhost:8000/sse</Text>）</li>
                    </ul>
                  </Paragraph>
                </>
              )
            }
          ]}
        />

        <div style={{ marginTop: 16, border: '1px solid #d9d9d9', borderRadius: '6px', overflow: 'hidden' }}>
          <Editor
            height="400px"
            defaultLanguage="json"
            theme="vs-dark"
            value={configContent}
            onChange={(value) => setConfigContent(value || '')}
            options={{
              fontSize: 14,
              fontFamily: 'JetBrains Mono, Consolas, Menlo, Monaco, monospace',
              lineNumbers: 'on',
              minimap: { enabled: false },
              scrollBeyondLastLine: false,
              automaticLayout: true,
              tabSize: 2,
              insertSpaces: true,
              wordWrap: 'on',
              formatOnPaste: true,
              formatOnType: true,
              folding: true,
              bracketPairColorization: { enabled: true },
              autoClosingBrackets: 'always',
              autoClosingQuotes: 'always',
              autoIndent: 'full',
              cursorBlinking: 'blink',
              cursorSmoothCaretAnimation: 'on',
              smoothScrolling: true,
              mouseWheelZoom: true,
              contextmenu: true,
              find: {
                addExtraSpaceOnTop: false,
                autoFindInSelection: 'never',
                seedSearchStringFromSelection: 'always'
              }
            }}
          />
        </div>
      </Modal>
    </div>
  );
};

export default MCPServersPage;