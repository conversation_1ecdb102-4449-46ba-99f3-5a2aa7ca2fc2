import React, { useState, useEffect, useCallback } from 'react';
import {
  Typography,
  Form,
  Input,
  Select,
  Card,
  Button,
  Table,
  Space,
  Modal,
  Tooltip,
  Tag,
  InputNumber,
  App,
  Row,
  Col,
  Spin,
  Alert,
  Dropdown,
  Badge,
  Divider,
  Pagination
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined,
  StarOutlined,
  EyeInvisibleOutlined,
  EyeOutlined,
  SendOutlined,
  ThunderboltOutlined,
  CopyOutlined,
  UndoOutlined,
  ApiOutlined,
  FilterOutlined,
  AppstoreOutlined,
  OrderedListOutlined,
  MoreOutlined,
  SearchOutlined
} from '@ant-design/icons';
import { modelConfigAPI } from '../../services/api/model';

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;
const { confirm } = Modal;

// 定义模型模态配置
const MODEL_MODALITIES = [
  { value: 'text_input', label: '文本输入', icon: '📄', color: 'blue', description: '接受文本输入' },
  { value: 'text_output', label: '文本输出', icon: '📄', color: 'blue', description: '生成文本输出' },
  { value: 'image_input', label: '图像输入', icon: '🖼️', color: 'purple', description: '接受图像输入' },
  { value: 'image_output', label: '图像输出', icon: '🖼️', color: 'purple', description: '生成图像输出' },
  { value: 'audio_input', label: '音频输入', icon: '🎵', color: 'orange', description: '接受音频输入' },
  { value: 'audio_output', label: '音频输出', icon: '🎵', color: 'orange', description: '生成音频输出' },
  { value: 'video_input', label: '视频输入', icon: '🎬', color: 'red', description: '接受视频输入' },
  { value: 'video_output', label: '视频输出', icon: '🎬', color: 'red', description: '生成视频输出' },
  { value: 'vector_output', label: '向量输出', icon: '📊', color: 'green', description: '生成向量嵌入输出' },
  { value: 'rerank_input', label: '重排序输入', icon: '🔄', color: 'orange', description: '接受查询和待排序文档列表' },
  { value: 'rerank_output', label: '重排序输出', icon: '📊', color: 'orange', description: '输出排序后的相关性分数列表' },
];

// 定义模型特性标签配置
const MODEL_CAPABILITIES = [
  { value: 'function_calling', label: '函数/工具调用', icon: '🔧', color: 'geekblue', description: '调用外部函数、工具和API' },
  { value: 'reasoning', label: '推理', icon: '🧠', color: 'gold', description: '逻辑推理和分析' },
];

// 定义提供商名称映射（包含实际数据中的提供商值）
const PROVIDER_NAMES = {
  // 标准化的提供商名称
  'openai': 'OpenAI',
  'anthropic': 'Anthropic',
  'google': 'Google AI (Gemini)',
  'azure': 'Azure OpenAI',
  'deepseek': 'DeepSeek',
  'aliyun': '阿里云 (通义千问)',
  'volcengine': '火山引擎 (豆包)',
  'gpustack': 'GPUStack',
  'ollama': 'Ollama',
  'custom': '自定义',
  // 实际数据中的提供商名称（大小写变体）
  'OpenAI': 'OpenAI',
  'Anthropic': 'Anthropic',
  'Google': 'Google AI (Gemini)',
  'Aliyun': '阿里云 (通义千问)',
  'DeepSeek': 'DeepSeek',
  'GPUStack': 'GPUStack',
  'Ollama': 'Ollama',
  'builtin': '内置模型'
};

const ModelConfigsPage = () => {
  const { message } = App.useApp();
  const [modelForm] = Form.useForm();
  const [testForm] = Form.useForm();
  const [modelLoading, setModelLoading] = useState(false);
  const [testLoading, setTestLoading] = useState(false);
  const [modelModalVisible, setModelModalVisible] = useState(false);
  const [modelConfigs, setModelConfigs] = useState([]);
  const [modelConfigsWithKeys, setModelConfigsWithKeys] = useState({});
  const [editingModel, setEditingModel] = useState(null);
  const [currentProvider, setCurrentProvider] = useState('openai');
  const [apiKeyVisible, setApiKeyVisible] = useState(false);
  const [testStatus, setTestStatus] = useState('idle'); // idle, loading, streaming, success, error, warning
  const [testResult, setTestResult] = useState('');

  // 默认模型设置相关状态
  const [defaultModelModalVisible, setDefaultModelModalVisible] = useState(false);
  const [defaultModelForm] = Form.useForm();
  const [defaultModelLoading, setDefaultModelLoading] = useState(false);
  const [currentDefaults, setCurrentDefaults] = useState({});

  const [ollamaModels, setOllamaModels] = useState([]);
  const [ollamaModelsLoading, setOllamaModelsLoading] = useState(false);
  const [gpustackModels, setGpustackModels] = useState([]);
  const [gpustackModelsLoading, setGpustackModelsLoading] = useState(false);
  const [anthropicModels, setAnthropicModels] = useState([]);
  const [anthropicModelsLoading, setAnthropicModelsLoading] = useState(false);
  const [googleModels, setGoogleModels] = useState([]);
  const [googleModelsLoading, setGoogleModelsLoading] = useState(false);
  const [xaiModels, setXaiModels] = useState([]);
  const [xaiModelsLoading, setXaiModelsLoading] = useState(false);
  const [testConnectionLoading, setTestConnectionLoading] = useState(false);

  // 新增状态：视图模式和过滤器
  const [viewMode, setViewMode] = useState('card'); // card 或 table
  const [selectedProviders, setSelectedProviders] = useState([]);
  const [selectedCapabilities, setSelectedCapabilities] = useState([]);
  const [searchKeyword, setSearchKeyword] = useState(''); // 搜索关键词

  // 分页状态
  const [cardPagination, setCardPagination] = useState({
    current: 1,
    pageSize: 12, // 每页显示12个卡片（4列 x 3行）
  });

  const fetchModelConfigs = useCallback(async () => {
    setModelLoading(true);
    try {
      // 获取不包含API密钥的模型配置列表（用于表格显示）
      const data = await modelConfigAPI.getAll();
      setModelConfigs(data);

      // 获取包含API密钥的模型配置（用于编辑）
      const dataWithKeys = await modelConfigAPI.getAll(true);

      // 将带有密钥的模型配置转换为以ID为键的对象，方便查找
      const configsWithKeysMap = {};
      dataWithKeys.forEach(config => {
        configsWithKeysMap[config.id] = config;
      });

      setModelConfigsWithKeys(configsWithKeysMap);
      console.log('已加载所有模型配置（包含API密钥）');

      setModelLoading(false);
    } catch (error) {
      console.error('获取模型配置失败:', error);
      message.error('获取模型配置失败');
      setModelLoading(false);
    }
  }, [message, setModelConfigs, setModelConfigsWithKeys, setModelLoading]);

  // 获取当前默认模型配置
  const fetchDefaultModels = useCallback(async () => {
    try {
      const defaults = await modelConfigAPI.getDefaults();
      setCurrentDefaults(defaults);

      // 设置表单默认值
      defaultModelForm.setFieldsValue({
        textModelId: defaults.text_model?.id,
        embeddingModelId: defaults.embedding_model?.id,
        rerankModelId: defaults.rerank_model?.id
      });
    } catch (error) {
      console.error('获取默认模型配置失败:', error);
      // 不显示错误消息，因为可能是首次使用没有设置默认模型
    }
  }, [defaultModelForm]);

  // 获取模型配置
  useEffect(() => {
    fetchModelConfigs();
    fetchDefaultModels();
  }, [fetchModelConfigs, fetchDefaultModels]);

  // 监听提供商变化，更新API密钥字段状态
  useEffect(() => {
    // 当模态框打开时，设置初始提供商值
    if (modelModalVisible) {
      const provider = modelForm.getFieldValue('provider');
      setCurrentProvider(provider);
    }
  }, [modelModalVisible, modelForm]);

  // 设置默认的测试提示词
  useEffect(() => {
    testForm.setFieldsValue({
      prompt: "你好，你是谁？",
      systemPrompt: "你是一个友好的助手，请用简洁明了的方式回答问题。在回答开始时请说'作为您的助手'。"
    });
  }, [testForm]);

  const showAddModelModal = () => {
    setEditingModel(null);
    modelForm.resetFields();
    // 设置默认值
    const defaultProvider = 'openai';
    modelForm.setFieldsValue({
      provider: defaultProvider,
      baseUrl: 'https://api.openai.com/v1',
      contextWindow: 16000, // 默认值，最小值为1，无最大值限制
      maxOutputTokens: 4000, // 默认值，最小值为1，无最大值限制
      requestTimeout: 60, // 默认值，范围10-300秒
      modalities: ['text_input', 'text_output'], // 默认文本输入输出模态
      capabilities: [], // 默认不设置特殊能力
      additionalParams: '{}'
    });
    // 设置当前提供商状态
    setCurrentProvider(defaultProvider);
    setApiKeyVisible(false); // 重置密钥可见性状态
    // 清空模型列表
    setOllamaModels([]);
    setGpustackModels([]);
    setAnthropicModels([]);
    setGoogleModels([]);
    setXaiModels([]);

    // 确保表单值设置完成后再显示模态框
    setTimeout(() => {
      setModelModalVisible(true);
    }, 0);
  };

  // 设置编辑模型表单的通用函数
  const setEditModelFormValues = (model, apiKey = '') => {
    // 处理Ollama URL显示格式
    let displayBaseUrl = model.base_url;
    if (model.provider === 'ollama') {
      displayBaseUrl = formatOllamaUrlForDisplay(model.base_url);
    }

    // 设置表单值
    modelForm.setFieldsValue({
      name: model.name,
      provider: model.provider,
      model_id: model.model_id,
      baseUrl: displayBaseUrl,
      apiKey: apiKey,
      contextWindow: model.context_window,
      maxOutputTokens: model.max_output_tokens,
      requestTimeout: model.request_timeout,
      modalities: model.modalities || ['text_input', 'text_output'],
      capabilities: model.capabilities || [],
      additionalParams: JSON.stringify(model.additional_params || {}, null, 2)
    });

    // 设置当前提供商状态
    setCurrentProvider(model.provider);
    setApiKeyVisible(false); // 重置密钥可见性状态

    // 根据提供商获取模型列表
    if (model.provider === 'ollama' && displayBaseUrl) {
      fetchOllamaModels(displayBaseUrl);
      setGpustackModels([]);
      setAnthropicModels([]);
      setGoogleModels([]);
    } else if (model.provider === 'gpustack' && displayBaseUrl) {
      fetchGpustackModels(displayBaseUrl, apiKey);
      setOllamaModels([]);
      setAnthropicModels([]);
      setGoogleModels([]);
    } else if (model.provider === 'anthropic' && displayBaseUrl && apiKey) {
      fetchAnthropicModels(displayBaseUrl, apiKey);
      setOllamaModels([]);
      setGpustackModels([]);
      setGoogleModels([]);
    } else if (model.provider === 'google' && displayBaseUrl && apiKey) {
      fetchGoogleModels(displayBaseUrl, apiKey);
      setOllamaModels([]);
      setGpustackModels([]);
      setAnthropicModels([]);
    } else {
      setOllamaModels([]);
      setGpustackModels([]);
      setAnthropicModels([]);
      setGoogleModels([]);
    }

    return displayBaseUrl;
  };

  const showEditModelModal = (model) => {
    setEditingModel(model);
    modelForm.resetFields(); // 先重置表单，避免显示旧数据

    // 从已加载的数据中获取带有API密钥的模型配置
    const modelWithKey = modelConfigsWithKeys[model.id];

    if (modelWithKey) {
      console.log('使用已加载的模型配置数据（包含API密钥）');
      setEditModelFormValues(model, modelWithKey.api_key || '');
      setModelModalVisible(true);
    } else {
      // 如果没有找到预加载的数据，则进行单独加载
      console.log('未找到预加载的模型配置数据，进行单独加载');
      setModelLoading(true);

      // 获取真实API密钥
      const fetchRealApiKey = async () => {
        try {
          const fetchedModelWithKey = await modelConfigAPI.getById(model.id, true);

          console.log('已获取真实API密钥');
          setEditModelFormValues(model, fetchedModelWithKey.api_key || '');

          // 更新缓存
          setModelConfigsWithKeys(prev => ({
            ...prev,
            [model.id]: fetchedModelWithKey
          }));

          // 数据加载完成后再显示模态框
          setModelLoading(false);
          setModelModalVisible(true);
        } catch (error) {
          console.error('获取API密钥失败:', error);
          message.error('获取API密钥失败');

          // 设置表单但不包含API密钥
          setEditModelFormValues(model, '');

          // 即使获取密钥失败，也设置好其他数据后再显示模态框
          setModelLoading(false);
          setModelModalVisible(true);
        }
      };

      // 执行获取真实密钥的操作
      fetchRealApiKey();
    }
  };

  const handleDeleteModel = (model) => {
    confirm({
      title: '确认删除',
      icon: <ExclamationCircleOutlined />,
      content: `确定要删除模型配置 "${model.name}" 吗？`,
      onOk: async () => {
        try {
          await modelConfigAPI.delete(model.id);
          message.success('模型配置删除成功');

          // 从缓存中删除该模型
          setModelConfigsWithKeys(prev => {
            const newCache = {...prev};
            delete newCache[model.id];
            return newCache;
          });

          fetchModelConfigs();
        } catch (error) {
          console.error('删除模型配置失败:', error);
          message.error('删除模型配置失败');
        }
      },
    });
  };

  const handleModelModalOk = async () => {
    try {
      const values = await modelForm.validateFields();

      // 处理additional_params
      let additionalParams = {};
      if (values.additionalParams && values.additionalParams.trim() !== '') {
        try {
          additionalParams = JSON.parse(values.additionalParams);
        } catch (error) {
          message.error('附加参数格式不正确，请输入有效的JSON');
          return;
        }
      }

      // 处理Ollama URL格式化
      let baseUrl = values.baseUrl;
      if (values.provider === 'ollama') {
        baseUrl = formatOllamaUrlForSave(baseUrl);
      }

      // 构建API请求数据
      const apiData = {
        name: values.name,
        provider: values.provider,
        model_id: values.model_id,
        base_url: baseUrl,
        context_window: parseInt(values.contextWindow),
        max_output_tokens: parseInt(values.maxOutputTokens),
        request_timeout: parseInt(values.requestTimeout),
        modalities: values.modalities || [],
        capabilities: values.capabilities || [],
        additional_params: additionalParams
      };

      // API密钥处理:
      // 1. 如果是Ollama，不需要密钥
      // 2. 如果是新建模型，使用输入的密钥
      // 3. 如果是编辑模型且apiKey字段不为空，使用新输入的密钥
      // 4. 如果是编辑模型且apiKey字段为空，不更新密钥(保留旧密钥)
      const needsApiKey = values.provider !== 'ollama';

      if (needsApiKey) {
        // 编辑模式下如果apiKey为空，则不传递api_key字段，这样后端不会更新密钥
        // 如果有值，则使用新输入的值
        if (editingModel) {
          if (values.apiKey && values.apiKey.trim() !== '') {
            apiData.api_key = values.apiKey;
          }
        } else {
          // 新建模式总是传递apiKey
          apiData.api_key = values.apiKey || '';
        }
      }

      setModelLoading(true);

      let updatedModel;

      if (editingModel) {
        // 编辑现有模型
        console.log('正在更新模型配置，API数据:', {...apiData, api_key: apiData.api_key ? '***已隐藏***' : undefined});
        updatedModel = await modelConfigAPI.update(editingModel.id, apiData);
        message.success('模型配置更新成功');

        // 更新缓存中的模型配置
        if (updatedModel) {
          // 更新带密钥的缓存
          setModelConfigsWithKeys(prev => {
            const newCache = {...prev};

            if (values.provider === 'ollama') {
              // Ollama不需要API密钥，缓存中也不存储
              newCache[editingModel.id] = {
                ...updatedModel,
                api_key: ''
              };
            } else {
              // 其他provider处理API密钥
              if (apiData.api_key) {
                newCache[editingModel.id] = {
                  ...updatedModel,
                  api_key: apiData.api_key
                };
              } else {
                // 保留原有密钥
                newCache[editingModel.id] = {
                  ...updatedModel,
                  api_key: prev[editingModel.id]?.api_key || ''
                };
              }
            }
            return newCache;
          });
        }
      } else {
        // 创建新模型
        if (needsApiKey && values.provider !== 'custom' && (!values.apiKey || values.apiKey.trim() === '')) {
          message.warning('警告：您没有提供API密钥。如果此服务需要密钥，测试将失败。');
        }
        console.log('正在创建新模型配置，API数据:', {...apiData, api_key: apiData.api_key ? '***已隐藏***' : undefined});
        updatedModel = await modelConfigAPI.create(apiData);
        message.success('模型配置创建成功');

        // 将新创建的模型添加到缓存
        if (updatedModel && updatedModel.id) {
          setModelConfigsWithKeys(prev => ({
            ...prev,
            [updatedModel.id]: {
              ...updatedModel,
              api_key: values.provider === 'ollama' ? '' : (apiData.api_key || '')
            }
          }));
        }
      }

      setModelModalVisible(false);
      fetchModelConfigs(); // 重新获取所有模型配置（不包含密钥的版本，用于表格显示）
      setModelLoading(false);
    } catch (error) {
      console.error('保存模型配置失败:', error);
      message.error('保存模型配置失败: ' + (error.response?.data?.error || error.message));
      setModelLoading(false);
    }
  };



  // 显示默认模型设置modal
  const showDefaultModelModal = () => {
    fetchDefaultModels(); // 重新获取最新的默认模型配置
    setDefaultModelModalVisible(true);
  };

  // 处理默认模型设置
  const handleDefaultModelOk = async () => {
    try {
      const values = await defaultModelForm.validateFields();

      if (!values.textModelId && !values.embeddingModelId && !values.rerankModelId) {
        message.warning('请至少选择一个默认模型');
        return;
      }

      setDefaultModelLoading(true);

      await modelConfigAPI.setDefaults(values.textModelId, values.embeddingModelId, values.rerankModelId);
      message.success('默认模型设置成功');

      setDefaultModelModalVisible(false);
      fetchDefaultModels(); // 重新获取默认模型配置
      fetchModelConfigs(); // 重新获取模型列表以更新显示
    } catch (error) {
      console.error('设置默认模型失败:', error);
      message.error('设置默认模型失败: ' + (error.response?.data?.error || error.message));
    } finally {
      setDefaultModelLoading(false);
    }
  };

  // 取消默认模型设置
  const handleDefaultModelCancel = () => {
    setDefaultModelModalVisible(false);
  };

  const handleModelModalCancel = () => {
    setApiKeyVisible(false); // 重置密钥可见性状态
    setModelModalVisible(false);
  };

  // 获取Ollama模型列表
  const fetchOllamaModels = async (baseUrl) => {
    if (!baseUrl) return;

    setOllamaModelsLoading(true);
    try {
      // 使用后端API代理请求，避免CORS问题
      const response = await modelConfigAPI.fetchOllamaModels(baseUrl);

      if (response.success) {
        const models = response.models || [];
        setOllamaModels(models);
        console.log('获取到Ollama模型列表:', models);
      } else {
        throw new Error(response.message || '获取模型列表失败');
      }
    } catch (error) {
      console.error('获取Ollama模型列表失败:', error);
      message.error(`获取Ollama模型列表失败: ${error.message}`);
      setOllamaModels([]);
    } finally {
      setOllamaModelsLoading(false);
    }
  };

  // 获取GPUStack模型列表
  const fetchGpustackModels = async (baseUrl, apiKey) => {
    if (!baseUrl || !apiKey) return;

    setGpustackModelsLoading(true);
    try {
      // 使用后端API代理请求，避免CORS问题
      const response = await modelConfigAPI.fetchGpustackModels(baseUrl, apiKey);

      if (response.success) {
        const models = response.models || [];
        setGpustackModels(models);
        console.log('获取到GPUStack模型列表:', models);
      } else {
        throw new Error(response.message || '获取模型列表失败');
      }
    } catch (error) {
      console.error('获取GPUStack模型列表失败:', error);
      message.error(`获取GPUStack模型列表失败: ${error.message}`);
      setGpustackModels([]);
    } finally {
      setGpustackModelsLoading(false);
    }
  };

  // 获取Anthropic模型列表
  const fetchAnthropicModels = async (baseUrl, apiKey) => {
    if (!baseUrl || !apiKey) return;

    setAnthropicModelsLoading(true);
    try {
      // 使用后端API代理请求，避免CORS问题
      const response = await modelConfigAPI.fetchAnthropicModels(baseUrl, apiKey);

      if (response.success) {
        const models = response.models || [];
        setAnthropicModels(models);
        console.log('获取到Anthropic模型列表:', models);
      } else {
        throw new Error(response.message || '获取模型列表失败');
      }
    } catch (error) {
      console.error('获取Anthropic模型列表失败:', error);
      message.error(`获取Anthropic模型列表失败: ${error.message}`);
      setAnthropicModels([]);
    } finally {
      setAnthropicModelsLoading(false);
    }
  };

  // 获取Google模型列表
  const fetchGoogleModels = async (baseUrl, apiKey) => {
    if (!baseUrl || !apiKey) return;

    setGoogleModelsLoading(true);
    try {
      // 使用后端API代理请求，避免CORS问题
      const response = await modelConfigAPI.fetchGoogleModels(baseUrl, apiKey);

      if (response.success) {
        const models = response.models || [];
        setGoogleModels(models);
        console.log('获取到Google模型列表:', models);
      } else {
        throw new Error(response.message || '获取模型列表失败');
      }
    } catch (error) {
      console.error('获取Google模型列表失败:', error);
      message.error(`获取Google模型列表失败: ${error.message}`);
      setGoogleModels([]);
    } finally {
      setGoogleModelsLoading(false);
    }
  };

  // 获取X.ai模型列表
  const fetchXaiModels = async (baseUrl, apiKey) => {
    if (!baseUrl || !apiKey) return;

    setXaiModelsLoading(true);
    try {
      // 使用后端API代理请求，避免CORS问题
      const response = await modelConfigAPI.fetchXaiModels(baseUrl, apiKey);

      if (response.success) {
        const models = response.models || [];
        setXaiModels(models);
        console.log('获取到X.ai模型列表:', models);
      } else {
        throw new Error(response.message || '获取模型列表失败');
      }
    } catch (error) {
      console.error('获取X.ai模型列表失败:', error);
      message.error(`获取X.ai模型列表失败: ${error.message}`);
      setXaiModels([]);
    } finally {
      setXaiModelsLoading(false);
    }
  };

  const handleProviderChange = (value) => {
    setCurrentProvider(value);

    // 根据提供商设置默认的baseUrl
    if (value === 'openai') {
      modelForm.setFieldsValue({ baseUrl: 'https://api.openai.com/v1' });
    } else if (value === 'anthropic') {
      modelForm.setFieldsValue({ baseUrl: 'https://api.anthropic.com' });
    } else if (value === 'google') {
      modelForm.setFieldsValue({ baseUrl: 'https://generativelanguage.googleapis.com/v1beta' });
      // 清空之前的模型列表
      setOllamaModels([]);
      setGpustackModels([]);
      setAnthropicModels([]);
      setGoogleModels([]);
      setXaiModels([]);
    } else if (value === 'ollama') {
      modelForm.setFieldsValue({ baseUrl: 'http://localhost:11434' });
      // 清空之前的模型列表
      setOllamaModels([]);
      setGpustackModels([]);
      setAnthropicModels([]);
      setGoogleModels([]);
      setXaiModels([]);
    } else if (value === 'azure') {
      modelForm.setFieldsValue({ baseUrl: 'https://{your-resource-name}.openai.azure.com' });
    } else if (value === 'xai') {
      modelForm.setFieldsValue({ baseUrl: 'https://api.x.ai/v1' });
    } else if (value === 'deepseek') {
      modelForm.setFieldsValue({ baseUrl: 'https://api.deepseek.com/v1' });
    } else if (value === 'aliyun') {
      modelForm.setFieldsValue({ baseUrl: 'https://dashscope.aliyuncs.com/compatible-mode/v1' });
    } else if (value === 'volcengine') {
      modelForm.setFieldsValue({ baseUrl: 'https://ark.cn-beijing.volces.com/api/v3' });
    } else if (value === 'gpustack') {
      modelForm.setFieldsValue({ baseUrl: 'http://localhost:80/v1-openai' });
      // 清空之前的模型列表
      setOllamaModels([]);
      setGpustackModels([]);
      setAnthropicModels([]);
      setGoogleModels([]);
      setXaiModels([]);
    }
  };



  // 处理Ollama模型选择
  const handleOllamaModelSelect = (modelName) => {
    if (modelName) {
      // 自动填充模型ID和名称
      modelForm.setFieldsValue({
        model_id: modelName,
        name: `Ollama - ${modelName}`
      });
    }
  };

  // 处理GPUStack模型选择
  const handleGpustackModelSelect = (modelId) => {
    if (modelId) {
      // 从模型列表中找到对应的模型信息
      const selectedModel = gpustackModels.find(model => model.id === modelId);
      if (selectedModel) {
        // 自动填充模型ID和名称
        modelForm.setFieldsValue({
          model_id: selectedModel.id,
          name: `GPUStack - ${selectedModel.id}`
        });
      }
    }
  };

  // 处理Anthropic模型选择
  const handleAnthropicModelSelect = (modelId) => {
    if (modelId) {
      // 从模型列表中找到对应的模型信息
      const selectedModel = anthropicModels.find(model => model.id === modelId);
      if (selectedModel) {
        // 自动填充模型ID和名称
        const displayName = selectedModel.display_name || selectedModel.id;
        modelForm.setFieldsValue({
          model_id: selectedModel.id,
          name: `Anthropic - ${displayName}`
        });
      }
    }
  };

  // 处理Google模型选择
  const handleGoogleModelSelect = (modelName) => {
    if (modelName) {
      // 从模型列表中找到对应的模型信息
      const selectedModel = googleModels.find(model => model.name === modelName);
      if (selectedModel) {
        // 自动填充模型ID和名称
        const displayName = selectedModel.displayName || selectedModel.baseModelId || selectedModel.name;
        modelForm.setFieldsValue({
          model_id: selectedModel.baseModelId || selectedModel.name,
          name: `Google - ${displayName}`
        });
      }
    }
  };

  // 处理X.ai模型选择
  const handleXaiModelSelect = (modelName) => {
    if (modelName) {
      // 从模型列表中找到对应的模型信息
      const selectedModel = xaiModels.find(model => model.id === modelName);
      if (selectedModel) {
        // 自动填充模型ID和名称
        modelForm.setFieldsValue({
          model_id: selectedModel.id,
          name: `X.ai - ${selectedModel.id}`
        });
      }
    }
  };

  // 格式化Ollama URL - 保存时添加/v1
  const formatOllamaUrlForSave = (url) => {
    if (!url) return url;

    // 移除末尾的斜杠
    let formattedUrl = url.replace(/\/+$/, '');

    // 如果已经包含/v1，不重复添加
    if (formattedUrl.endsWith('/v1')) {
      return formattedUrl;
    }

    // 添加/v1
    return `${formattedUrl}/v1`;
  };

  // 格式化Ollama URL - 编辑时移除/v1显示
  const formatOllamaUrlForDisplay = (url) => {
    if (!url) return url;

    // 移除末尾的/v1和斜杠
    return url.replace(/\/v1\/?$/, '').replace(/\/+$/, '');
  };

  // 测试连接
  const handleTestConnection = async () => {
    const baseUrl = modelForm.getFieldValue('baseUrl');
    if (!baseUrl) {
      message.warning('请先输入API基础URL');
      return;
    }

    setTestConnectionLoading(true);

    try {
      // 获取API密钥（如果需要的话）
      const apiKey = modelForm.getFieldValue('apiKey') || '';

      // 使用后端API测试连接，避免CORS问题
      const response = await modelConfigAPI.testConnection(baseUrl, currentProvider, apiKey);

      if (response.success) {
        message.success('连接测试成功！');

        // 根据提供商类型，连接成功后自动获取模型列表
        if (currentProvider === 'ollama') {
          fetchOllamaModels(baseUrl);
        } else if (currentProvider === 'gpustack') {
          const apiKey = modelForm.getFieldValue('apiKey');
          if (apiKey) {
            fetchGpustackModels(baseUrl, apiKey);
          } else {
            message.warning('请先输入API密钥以获取GPUStack模型列表');
          }
        } else if (currentProvider === 'anthropic') {
          const apiKey = modelForm.getFieldValue('apiKey');
          if (apiKey) {
            fetchAnthropicModels(baseUrl, apiKey);
          } else {
            message.warning('请先输入API密钥以获取Anthropic模型列表');
          }
        } else if (currentProvider === 'google') {
          const apiKey = modelForm.getFieldValue('apiKey');
          if (apiKey) {
            fetchGoogleModels(baseUrl, apiKey);
          } else {
            message.warning('请先输入API密钥以获取Google模型列表');
          }
        } else if (currentProvider === 'xai') {
          const apiKey = modelForm.getFieldValue('apiKey');
          if (apiKey) {
            fetchXaiModels(baseUrl, apiKey);
          } else {
            message.warning('请先输入API密钥以获取X.ai模型列表');
          }
        }
      } else {
        throw new Error(response.message || '连接测试失败');
      }
    } catch (error) {
      console.error('连接测试失败:', error);
      message.error(`连接测试失败: ${error.message}`);
    } finally {
      setTestConnectionLoading(false);
    }
  };

  const modelColumns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <Space>
          {text}
          {record.is_default_text && <Tag color="blue">默认文本</Tag>}
          {record.is_default_embedding && <Tag color="green">默认嵌入</Tag>}
          {record.is_default_rerank && <Tag color="orange">默认重排序</Tag>}
        </Space>
      )
    },
    {
      title: '提供商',
      dataIndex: 'provider',
      key: 'provider',
      render: (provider) => PROVIDER_NAMES[provider] || provider,
    },
    {
      title: '模型ID',
      dataIndex: 'model_id',
      key: 'model_id',
    },
    {
      title: '模态',
      dataIndex: 'modalities',
      key: 'modalities',
      render: (modalities) => {
        const inputModalities = (modalities || []).filter(mod => mod.includes('_input'));
        const outputModalities = (modalities || []).filter(mod => mod.includes('_output'));

        return (
          <div>
            {inputModalities.length > 0 && (
              <div style={{ marginBottom: 4 }}>
                <Text type="secondary" style={{ fontSize: '11px', marginRight: 4 }}>输入:</Text>
                <Space size={[0, 2]} wrap>
                  {inputModalities.map(mod => {
                    const modConfig = MODEL_MODALITIES.find(m => m.value === mod) || { label: mod, icon: '?', color: 'default' };
                    return (
                      <Tag color={modConfig.color} size="small" key={mod}>
                        <span style={{ marginRight: 2 }}>{modConfig.icon}</span>
                        {modConfig.label.replace('输入', '')}
                      </Tag>
                    );
                  })}
                </Space>
              </div>
            )}
            {outputModalities.length > 0 && (
              <div>
                <Text type="secondary" style={{ fontSize: '11px', marginRight: 4 }}>输出:</Text>
                <Space size={[0, 2]} wrap>
                  {outputModalities.map(mod => {
                    const modConfig = MODEL_MODALITIES.find(m => m.value === mod) || { label: mod, icon: '?', color: 'default' };
                    return (
                      <Tag color={modConfig.color} size="small" key={mod}>
                        <span style={{ marginRight: 2 }}>{modConfig.icon}</span>
                        {modConfig.label.replace('输出', '')}
                      </Tag>
                    );
                  })}
                </Space>
              </div>
            )}
          </div>
        );
      }
    },
    {
      title: '特性标签',
      dataIndex: 'capabilities',
      key: 'capabilities',
      render: (capabilities) => (
        <Space size={[0, 4]} wrap>
          {(capabilities || []).map(cap => {
            const capConfig = MODEL_CAPABILITIES.find(c => c.value === cap) || { label: cap, icon: '?', color: 'default' };
            return (
              <Tag color={capConfig.color} key={cap}>
                <span style={{ marginRight: 4 }}>{capConfig.icon}</span>
                {capConfig.label}
              </Tag>
            );
          })}
        </Space>
      )
    },
    {
      title: '上下文窗口',
      dataIndex: 'context_window',
      key: 'context_window',
      render: (text) => `${text.toLocaleString()} tokens`
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => showEditModelModal(record)}
          >
            编辑
          </Button>

          <Button
            type="text"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDeleteModel(record)}
            disabled={record.is_default_text || record.is_default_embedding || record.is_default_rerank}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  // 测试模型
  const handleTestModel = async () => {
    try {
      const values = await testForm.validateFields();

      // 检查是否选择了模型
      if (!values.modelId) {
        message.warning('请先选择要测试的模型');
        return;
      }

      setTestLoading(true);
      setTestStatus('loading');
      setTestResult('');

      try {
        // 获取选中的模型配置
        const selectedModelConfig = modelConfigs.find(m => m.id.toString() === values.modelId.toString());

        if (!selectedModelConfig) {
          throw new Error('未找到所选模型配置');
        }

        console.log('选中的模型配置:', {
          ...selectedModelConfig,
          api_key: selectedModelConfig.api_key ? '***已隐藏***' : undefined
        });

        // 使用流式API替代常规API
        let streamContent = '';
        let receivedFirstResponse = false;

        // 添加调试日志
        console.log("[模型测试] 发送参数:", {
          modelId: selectedModelConfig.id,
          prompt: values.prompt,
          systemPrompt: values.systemPrompt
        });

        // 真正的流式响应处理
        await modelConfigAPI.testModelStream(
          selectedModelConfig.id,
          values.prompt,
          (chunk, meta) => {
            console.log("[模型测试] 收到流式数据:", {
              hasChunk: !!chunk,
              chunkLength: chunk?.length,
              meta
            });

            // 处理流式内容
            if (chunk) {
              // 无论收到多少字符，都立即更新UI
              receivedFirstResponse = true;
              streamContent += chunk;
              setTestResult(streamContent);

              // 收到第一个响应后，将状态从loading改为streaming
              if (testStatus === 'loading') {
                setTestStatus('streaming');
              }
            }

            // 处理连接状态
            if (meta && meta.connectionStatus) {
              if (meta.connectionStatus === 'error' && meta.error) {
                setTestResult(`测试失败: ${meta.error}`);
                setTestStatus('error');
              } else if (meta.connectionStatus === 'done') {
                console.log("[模型测试] 流式响应已完成");
                setTestStatus('success');
              }
            }
          },
          values.systemPrompt // 传递表单中的系统提示
        );

        // 如果API调用完成但未收到任何内容
        if (!receivedFirstResponse && streamContent === '') {
          console.warn("[模型测试] 警告: 未接收到任何内容");
          if (!testResult) {
            setTestResult('测试完成，但未收到任何响应，请检查模型配置');
            setTestStatus('warning');
          }
        }
      } catch (error) {
        console.error('测试LLM失败:', error);
        setTestResult(`测试失败: ${error.message || '未知错误'}`);
        setTestStatus('error');
      } finally {
        setTestLoading(false);
      }
    } catch (error) {
      message.error('请先完成表单填写');
    }
  };

  // 复制测试结果
  const handleCopyTestResult = () => {
    if (testResult) {
      navigator.clipboard.writeText(testResult)
        .then(() => {
          message.success('已复制到剪贴板');
        })
        .catch(err => {
          console.error('复制失败:', err);
          message.error('复制失败');
        });
    }
  };

  // 重置测试表单
  const handleResetTest = () => {
    testForm.resetFields();
    setTestResult('');
    setTestStatus('idle');

    // 设置默认值
    testForm.setFieldsValue({
      prompt: "你好，你是谁？",
      systemPrompt: "你是一个友好的助手，请用简洁明了的方式回答问题。在回答开始时请说'作为您的助手'。"
    });
  };

  // 过滤和排序模型配置
  const getFilteredModelConfigs = () => {
    return modelConfigs.filter(model => {
      // 搜索关键词过滤
      if (searchKeyword.trim()) {
        const keyword = searchKeyword.toLowerCase();
        const matchesName = model.name.toLowerCase().includes(keyword);
        const matchesModelId = model.model_id.toLowerCase().includes(keyword);
        const matchesProvider = (PROVIDER_NAMES[model.provider] || model.provider).toLowerCase().includes(keyword);

        if (!matchesName && !matchesModelId && !matchesProvider) {
          return false;
        }
      }

      // 提供商过滤
      if (selectedProviders.length > 0 && !selectedProviders.includes(model.provider)) {
        return false;
      }

      // 能力标签过滤
      if (selectedCapabilities.length > 0) {
        const modelCapabilities = model.capabilities || [];
        const hasMatchingCapability = selectedCapabilities.some(cap =>
          modelCapabilities.includes(cap)
        );
        if (!hasMatchingCapability) {
          return false;
        }
      }

      return true;
    }).sort((a, b) => {
      // 按名称排序（不区分大小写）
      return a.name.toLowerCase().localeCompare(b.name.toLowerCase());
    });
  };

  // 当过滤条件改变时，重置分页到第一页
  useEffect(() => {
    setCardPagination(prev => ({ ...prev, current: 1 }));
  }, [selectedProviders, selectedCapabilities, searchKeyword]);

  // 获取提供商统计
  const getProviderStats = () => {
    const stats = {};
    modelConfigs.forEach(model => {
      stats[model.provider] = (stats[model.provider] || 0) + 1;
    });
    return stats;
  };

  // 获取能力标签统计
  const getCapabilityStats = () => {
    const stats = {};
    modelConfigs.forEach(model => {
      (model.capabilities || []).forEach(cap => {
        stats[cap] = (stats[cap] || 0) + 1;
      });
    });
    return stats;
  };

  // 渲染过滤器
  const renderFilters = () => {
    const providerStats = getProviderStats();
    const capabilityStats = getCapabilityStats();

    // 只显示实际存在的提供商
    const existingProviders = Object.keys(providerStats).filter(provider => providerStats[provider] > 0);
    const providerItems = existingProviders.map(provider => ({
      key: provider,
      label: (
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <span>{PROVIDER_NAMES[provider] || provider}</span>
          <Badge count={providerStats[provider]} size="small" />
        </div>
      ),
    }));

    // 只显示实际存在的能力标签
    const existingCapabilities = Object.keys(capabilityStats).filter(cap => capabilityStats[cap] > 0);
    const capabilityItems = existingCapabilities.map(capValue => {
      const capConfig = MODEL_CAPABILITIES.find(c => c.value === capValue) ||
        { value: capValue, label: capValue, color: 'default' };
      return {
        key: capValue,
        label: (
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Tag color={capConfig.color} size="small">{capConfig.label}</Tag>
            <Badge count={capabilityStats[capValue]} size="small" />
          </div>
        ),
      };
    });

    return (
      <Space size="middle">
        {/* 搜索框 */}
        <Input
          placeholder="搜索模型名称、ID或提供商..."
          prefix={<SearchOutlined />}
          value={searchKeyword}
          onChange={(e) => setSearchKeyword(e.target.value)}
          allowClear
          style={{
            width: 240,
            borderRadius: '6px',
            height: '32px'
          }}
        />

        {existingProviders.length > 0 && (
          <Dropdown
            menu={{
              items: providerItems,
              selectable: true,
              multiple: true,
              selectedKeys: selectedProviders,
              onSelect: ({ selectedKeys }) => setSelectedProviders(selectedKeys),
              onDeselect: ({ selectedKeys }) => setSelectedProviders(selectedKeys),
            }}
            trigger={['click']}
          >
            <Button
              icon={<FilterOutlined />}
              type={selectedProviders.length > 0 ? "primary" : "default"}
              style={{
                borderRadius: '6px',
                height: '32px'
              }}
            >
              提供商 {selectedProviders.length > 0 && (
                <Badge
                  count={selectedProviders.length}
                  size="small"
                  style={{
                    backgroundColor: '#fff',
                    color: '#1677ff',
                    marginLeft: '4px'
                  }}
                />
              )}
            </Button>
          </Dropdown>
        )}

        {existingCapabilities.length > 0 && (
          <Dropdown
            menu={{
              items: capabilityItems,
              selectable: true,
              multiple: true,
              selectedKeys: selectedCapabilities,
              onSelect: ({ selectedKeys }) => setSelectedCapabilities(selectedKeys),
              onDeselect: ({ selectedKeys }) => setSelectedCapabilities(selectedKeys),
            }}
            trigger={['click']}
          >
            <Button
              icon={<FilterOutlined />}
              type={selectedCapabilities.length > 0 ? "primary" : "default"}
              style={{
                borderRadius: '6px',
                height: '32px'
              }}
            >
              能力标签 {selectedCapabilities.length > 0 && (
                <Badge
                  count={selectedCapabilities.length}
                  size="small"
                  style={{
                    backgroundColor: '#fff',
                    color: '#1677ff',
                    marginLeft: '4px'
                  }}
                />
              )}
            </Button>
          </Dropdown>
        )}

        {(searchKeyword.trim() || selectedProviders.length > 0 || selectedCapabilities.length > 0) && (
          <Button
            type="text"
            onClick={() => {
              setSearchKeyword('');
              setSelectedProviders([]);
              setSelectedCapabilities([]);
            }}
            style={{
              color: '#ff4d4f',
              borderRadius: '6px',
              height: '32px'
            }}
          >
            清除筛选
          </Button>
        )}
      </Space>
    );
  };

  // 渲染紧凑卡片视图
  const renderCardView = () => {
    // 如果正在加载，显示标准加载动画
    if (modelLoading) {
      return (
        <div style={{ position: 'relative', minHeight: '400px' }}>
          {/* 加载指示器 - 绝对定位，不影响布局 */}
          <div style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            zIndex: 1000,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            gap: '12px'
          }}>
            <Spin size="large" />
            <div style={{ color: '#1677ff', fontSize: '14px' }}>加载模型配置列表</div>
          </div>
        </div>
      );
    }

    const filteredConfigs = getFilteredModelConfigs();

    if (filteredConfigs.length === 0) {
      return (
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <Text type="secondary">
            {modelConfigs.length === 0 ? '暂无模型配置' : '没有符合筛选条件的模型'}
          </Text>
        </div>
      );
    }

    // 计算分页数据
    const { current, pageSize } = cardPagination;
    const startIndex = (current - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedConfigs = filteredConfigs.slice(startIndex, endIndex);

    return (
      <div>
        <Row gutter={[16, 16]} style={{ minHeight: '400px' }}>
          {paginatedConfigs.map(model => (
          <Col xs={24} sm={12} lg={8} xl={6} key={model.id}>
            <Card
              size="small"
              hoverable
              style={{
                height: '100%',
                borderRadius: '8px',
                border: (() => {
                  if (model.is_default_text) return '2px solid #1677ff';
                  if (model.is_default_embedding) return '2px solid #52c41a';
                  if (model.is_default_rerank) return '2px solid #fa8c16';
                  return '1px solid #d9d9d9';
                })(),
                display: 'flex',
                flexDirection: 'column'
              }}
              styles={{
                body: {
                  padding: '12px',
                  flex: 1,
                  display: 'flex',
                  flexDirection: 'column'
                }
              }}
              actions={[
                <Tooltip title="编辑">
                  <Button
                    type="text"
                    icon={<EditOutlined />}
                    size="small"
                    onClick={() => showEditModelModal(model)}
                  />
                </Tooltip>,
                <Dropdown
                  menu={{
                    items: [
                      {
                        key: 'test',
                        label: '测试模型',
                        icon: <ThunderboltOutlined />,
                        onClick: () => {
                          testForm.setFieldsValue({ modelId: model.id });
                          // 滚动到测试区域
                          document.querySelector('.model-test-section')?.scrollIntoView({
                            behavior: 'smooth'
                          });
                        }
                      },
                      {
                        key: 'delete',
                        label: '删除',
                        icon: <DeleteOutlined />,
                        danger: true,
                        disabled: model.is_default_text || model.is_default_embedding || model.is_default_rerank,
                        onClick: () => handleDeleteModel(model)
                      }
                    ]
                  }}
                  trigger={['click']}
                >
                  <Tooltip title="更多操作">
                    <Button type="text" icon={<MoreOutlined />} size="small" />
                  </Tooltip>
                </Dropdown>
              ]}
            >
              <div style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
                <div style={{ marginBottom: '8px' }}>
                  <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Text strong style={{ fontSize: '14px' }}>{model.name}</Text>
                    {model.is_default_text && (
                      <Tag color="blue" size="small">默认文本生成</Tag>
                    )}
                    {model.is_default_embedding && (
                      <Tag color="green" size="small">默认嵌入</Tag>
                    )}
                    {model.is_default_rerank && (
                      <Tag color="orange" size="small">默认重排序</Tag>
                    )}
                  </div>
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    {PROVIDER_NAMES[model.provider] || model.provider}
                  </Text>
                </div>

                <div style={{ marginBottom: '8px' }}>
                  <Text type="secondary" style={{ fontSize: '11px' }}>模型ID:</Text>
                  <br />
                  <Text style={{ fontSize: '12px', wordBreak: 'break-all' }}>
                    {model.model_id}
                  </Text>
                </div>

                {/* 模态显示 */}
                <div style={{ marginBottom: '8px' }}>
                  <Text type="secondary" style={{ fontSize: '11px' }}>模态:</Text>
                  <br />
                  {(() => {
                    const inputModalities = (model.modalities || []).filter(mod => mod.includes('_input'));
                    const outputModalities = (model.modalities || []).filter(mod => mod.includes('_output'));

                    return (
                      <div style={{ display: 'flex', gap: '8px' }}>
                        {/* 输入列 */}
                        <div style={{ flex: 1, minWidth: 0 }}>
                          {inputModalities.length > 0 && (
                            <>
                              <Text type="secondary" style={{ fontSize: '10px', display: 'block', marginBottom: 2 }}>输入:</Text>
                              <Space size={[2, 2]} wrap>
                                {inputModalities.map(mod => {
                                  const modConfig = MODEL_MODALITIES.find(m => m.value === mod) ||
                                    { label: mod, icon: '?', color: 'default' };
                                  return (
                                    <Tag color={modConfig.color} size="small" key={mod}>
                                      <span style={{ marginRight: 2 }}>{modConfig.icon}</span>
                                      {modConfig.label.replace('输入', '')}
                                    </Tag>
                                  );
                                })}
                              </Space>
                            </>
                          )}
                        </div>

                        {/* 输出列 */}
                        <div style={{ flex: 1, minWidth: 0 }}>
                          {outputModalities.length > 0 && (
                            <>
                              <Text type="secondary" style={{ fontSize: '10px', display: 'block', marginBottom: 2 }}>输出:</Text>
                              <Space size={[2, 2]} wrap>
                                {outputModalities.map(mod => {
                                  const modConfig = MODEL_MODALITIES.find(m => m.value === mod) ||
                                    { label: mod, icon: '?', color: 'default' };
                                  return (
                                    <Tag color={modConfig.color} size="small" key={mod}>
                                      <span style={{ marginRight: 2 }}>{modConfig.icon}</span>
                                      {modConfig.label.replace('输出', '')}
                                    </Tag>
                                  );
                                })}
                              </Space>
                            </>
                          )}
                        </div>
                      </div>
                    );
                  })()}
                </div>

                {/* 特性标签显示 */}
                <div style={{ marginBottom: '8px' }}>
                  <Text type="secondary" style={{ fontSize: '11px' }}>特性:</Text>
                  <br />
                  <Space size={[4, 4]} wrap>
                    {(model.capabilities || []).map(cap => {
                      const capConfig = MODEL_CAPABILITIES.find(c => c.value === cap) ||
                        { label: cap, icon: '?', color: 'default' };
                      return (
                        <Tag color={capConfig.color} size="small" key={cap}>
                          <span style={{ marginRight: 2 }}>{capConfig.icon}</span>
                          {capConfig.label}
                        </Tag>
                      );
                    })}
                  </Space>
                </div>

                {/* 使用 marginTop: 'auto' 将底部信息推到底部 */}
                <div style={{ fontSize: '11px', color: '#666', marginTop: 'auto' }}>
                  <div>上下文: {model.context_window?.toLocaleString()} tokens</div>
                  <div>最大输出: {model.max_output_tokens?.toLocaleString()} tokens</div>
                </div>
              </div>
            </Card>
          </Col>
          ))}
        </Row>

        {/* 分页组件 - 右下角对齐 */}
        {filteredConfigs.length > pageSize && (
          <div style={{ display: 'flex', justifyContent: 'flex-end', marginTop: '24px' }}>
            <Pagination
              current={current}
              pageSize={pageSize}
              total={filteredConfigs.length}
              showSizeChanger={false}
              showQuickJumper={false}
              showTotal={(total, range) => `第 ${range[0]}-${range[1]} 项，共 ${total} 个模型`}
              onChange={(page) => {
                setCardPagination({ current: page, pageSize });
              }}
              size="default"
            />
          </div>
        )}
      </div>
    );
  };

  // 渲染表格视图（保持原有的表格）
  const renderTableView = () => {
    const filteredConfigs = getFilteredModelConfigs();

    return (
      <Table
        columns={modelColumns}
        dataSource={filteredConfigs}
        rowKey="id"
        loading={modelLoading}
        pagination={false}
        size="small"
      />
    );
  };

  return (
    <div>
      <div style={{ marginBottom: '24px' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
          <div>
            <Title level={4} style={{ margin: 0, marginBottom: '8px' }}>模型服务配置</Title>
            <Text type="secondary">
              配置系统使用的大语言模型服务，包括API密钥、模型参数等
            </Text>
          </div>
          <Space size="middle">
            <Button
              icon={<StarOutlined />}
              onClick={showDefaultModelModal}
              size="large"
              style={{
                borderRadius: '8px',
                height: '42px',
                fontSize: '14px'
              }}
            >
              设置默认模型
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={showAddModelModal}
              size="large"
              style={{
                borderRadius: '8px',
                height: '42px',
                fontSize: '14px'
              }}
            >
              添加模型
            </Button>
          </Space>
        </div>
      </div>
      <Card
        title="模型列表"
        extra={
          <Space size="middle">
            {renderFilters()}
            <Divider type="vertical" style={{ height: '20px', margin: '0 8px' }} />
            <Space.Compact>
              <Button
                type={viewMode === 'card' ? 'primary' : 'default'}
                icon={<AppstoreOutlined />}
                onClick={() => setViewMode('card')}
                style={{
                  borderRadius: '6px 0 0 6px',
                  fontSize: '14px',
                  height: '32px',
                  minWidth: '40px'
                }}
                title="卡片视图"
              />
              <Button
                type={viewMode === 'table' ? 'primary' : 'default'}
                icon={<OrderedListOutlined />}
                onClick={() => setViewMode('table')}
                style={{
                  borderRadius: '0 6px 6px 0',
                  fontSize: '14px',
                  height: '32px',
                  minWidth: '40px'
                }}
                title="列表视图"
              />
            </Space.Compact>
          </Space>
        }
        variant="borderless"
        style={{
          borderRadius: '12px',
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.05)',
          marginBottom: '24px'
        }}
      >
        {viewMode === 'card' ? renderCardView() : renderTableView()}
      </Card>

      {/* 模型测试区域 */}
      <Card
        className="model-test-section"
        title={
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <ThunderboltOutlined style={{ marginRight: 8, color: '#1890ff' }} />
            <span>模型测试</span>
          </div>
        }
        variant="borderless"
        style={{
          borderRadius: '12px',
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.05)'
        }}
      >
        <Row gutter={24}>
          <Col xs={24} md={12}>
            <Form
              form={testForm}
              layout="vertical"
            >
              <Form.Item
                name="modelId"
                label="选择模型"
                rules={[{ required: true, message: '请选择要测试的模型' }]}
              >
                <Select placeholder="选择要测试的模型">
                  {modelConfigs.map(model => (
                    <Option key={model.id} value={model.id}>
                      {model.name} ({model.provider}/{model.model_id})
                      {model.is_default_text && <Tag color="blue" style={{ marginLeft: 8 }}>默认文本</Tag>}
                      {model.is_default_embedding && <Tag color="green" style={{ marginLeft: 8 }}>默认嵌入</Tag>}
                      {model.is_default_rerank && <Tag color="orange" style={{ marginLeft: 8 }}>默认重排序</Tag>}
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item
                name="systemPrompt"
                label={
                  <span>
                    系统提示词
                    <Tooltip title="设置模型的系统提示词，定义其基础行为">
                      <InfoCircleOutlined style={{ marginLeft: 8 }} />
                    </Tooltip>
                  </span>
                }
              >
                <TextArea
                  rows={2}
                  placeholder="输入系统提示词..."
                />
              </Form.Item>

              <Form.Item
                name="prompt"
                label="测试提示词"
                rules={[{ required: true, message: '请输入测试提示词' }]}
              >
                <TextArea
                  rows={3}
                  placeholder="输入测试提示词..."
                />
              </Form.Item>

              <Form.Item>
                <Space>
                  <Button
                    type="primary"
                    icon={<SendOutlined />}
                    onClick={handleTestModel}
                    loading={testLoading}
                  >
                    发送测试
                  </Button>
                  <Button
                    icon={<UndoOutlined />}
                    onClick={handleResetTest}
                  >
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </Col>

          <Col xs={24} md={12}>
            <div style={{ marginBottom: 16 }}>
              <Title level={5}>测试结果</Title>
            </div>

            {testResult || testStatus === 'loading' ? (
              <div>
                <div
                  style={{
                    border: '1px solid #d9d9d9',
                    borderRadius: '4px',
                    padding: '12px',
                    minHeight: '200px',
                    maxHeight: '300px',
                    overflowY: 'auto',
                    backgroundColor: '#fafafa',
                    marginBottom: '16px',
                    whiteSpace: 'pre-wrap',
                    wordBreak: 'break-word',
                    position: 'relative'
                  }}
                >
                  {testResult}
                  {(testStatus === 'streaming' || testStatus === 'loading') && (
                    <span
                      style={{
                        display: 'inline-block',
                        width: '2px',
                        height: '14px',
                        backgroundColor: '#1677ff',
                        animation: 'blink 1s infinite',
                        marginLeft: '2px',
                        verticalAlign: 'middle',
                        position: 'absolute'
                      }}
                    />
                  )}
                </div>



                <Space>
                  <Button
                    icon={<CopyOutlined />}
                    onClick={handleCopyTestResult}
                  >
                    复制结果
                  </Button>
                </Space>
              </div>
            ) : (
              <Alert
                message="等待测试"
                description="选择一个模型并输入测试提示词，然后点击'发送测试'按钮开始测试。"
                type="info"
                showIcon
              />
            )}
          </Col>
        </Row>
      </Card>

      {/* 添加/编辑模型模态框 */}
      <Modal
        title={editingModel ? '编辑模型配置' : '添加模型配置'}
        open={modelModalVisible}
        onOk={handleModelModalOk}
        onCancel={handleModelModalCancel}
        width={700}
        confirmLoading={modelLoading}
        destroyOnHidden
      >
        <Form
          form={modelForm}
          layout="vertical"
        >
          <div style={{ marginBottom: '24px' }}>
            <Title level={5}>基本信息</Title>
          </div>

          {/* 提供商选择 - 放在最上面 */}
          <Row gutter={24}>
            <Col span={24}>
              <Form.Item
                name="provider"
                label="提供商"
                rules={[{ required: true, message: '请选择模型提供商' }]}
              >
                <Select onChange={handleProviderChange} placeholder="选择模型提供商">
                  <Option value="openai">OpenAI</Option>
                  <Option value="anthropic">Anthropic</Option>
                  <Option value="google">Google AI (Gemini)</Option>
                  <Option value="azure">Azure OpenAI</Option>
                  <Option value="xai">X.ai (Grok)</Option>
                  <Option value="deepseek">DeepSeek</Option>
                  <Option value="aliyun">阿里云 (通义千问)</Option>
                  <Option value="volcengine">火山引擎 (豆包)</Option>
                  <Option value="gpustack">GPUStack</Option>
                  <Option value="ollama">Ollama</Option>
                  <Option value="custom">自定义</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          {/* API基础URL */}
          <Row gutter={24}>
            <Col span={20}>
              <Form.Item
                name="baseUrl"
                label="API基础URL"
                rules={[{ required: true, message: '请输入API基础URL' }]}
                tooltip="API URL采用OpenAI兼容格式，调用时会添加/chat/completions路径。Ollama需要输入基础URL，系统会自动补全为/v1/chat/completions。"
              >
                <Input
                  placeholder="例如: https://api.openai.com/v1"
                />
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item label=" ">
                <Button
                  type="default"
                  icon={<ApiOutlined />}
                  onClick={handleTestConnection}
                  loading={testConnectionLoading}
                  style={{ width: '100%', height: '32px' }}
                >
                  测试连接
                </Button>
              </Form.Item>
            </Col>
          </Row>

          {/* Ollama模型选择 */}
          {currentProvider === 'ollama' && (
            <Row gutter={24}>
              <Col span={24}>
                <Form.Item
                  label={
                    <span>
                      选择Ollama模型
                      <Tooltip title="从Ollama服务器获取可用模型列表">
                        <InfoCircleOutlined style={{ marginLeft: 8 }} />
                      </Tooltip>
                    </span>
                  }
                >
                  <Select
                    placeholder="选择模型"
                    loading={ollamaModelsLoading}
                    onChange={handleOllamaModelSelect}
                    disabled={ollamaModels.length === 0 && !ollamaModelsLoading}
                    notFoundContent={ollamaModelsLoading ? <Spin size="small" /> : "无可用模型"}
                    optionLabelProp="label"
                  >
                    {ollamaModels.map(model => (
                      <Option key={model.name} value={model.name} label={model.name}>
                        <div>
                          <div style={{ fontWeight: 'bold' }}>{model.name}</div>
                          {model.details && (
                            <div style={{ fontSize: '12px', color: '#666' }}>
                              大小: {model.size ? `${(model.size / 1024 / 1024 / 1024).toFixed(1)}GB` : '未知'}
                              {model.details.family && ` | 系列: ${model.details.family}`}
                            </div>
                          )}
                        </div>
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>
          )}

          {/* GPUStack模型选择 */}
          {currentProvider === 'gpustack' && (
            <Row gutter={24}>
              <Col span={24}>
                <Form.Item
                  label={
                    <span>
                      选择GPUStack模型
                      <Tooltip title="输入API密钥后点击'测试连接'按钮获取可用模型列表">
                        <InfoCircleOutlined style={{ marginLeft: 8 }} />
                      </Tooltip>
                    </span>
                  }
                >
                  <Select
                    placeholder="请先测试连接以获取模型列表"
                    loading={gpustackModelsLoading}
                    onChange={handleGpustackModelSelect}
                    disabled={gpustackModels.length === 0 && !gpustackModelsLoading}
                    notFoundContent={gpustackModelsLoading ? <Spin size="small" /> : "请先测试连接"}
                    optionLabelProp="label"
                  >
                    {gpustackModels.map(model => (
                      <Option key={model.id} value={model.id} label={model.id}>
                        <div>
                          <div style={{ fontWeight: 'bold' }}>{model.id}</div>
                          {model.object && (
                            <div style={{ fontSize: '12px', color: '#666' }}>
                              类型: {model.object}
                              {model.owned_by && ` | 提供商: ${model.owned_by}`}
                            </div>
                          )}
                        </div>
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>
          )}

          {/* Anthropic模型选择 */}
          {currentProvider === 'anthropic' && (
            <Row gutter={24}>
              <Col span={24}>
                <Form.Item
                  label={
                    <span>
                      选择Anthropic模型
                      <Tooltip title="输入API密钥后点击'测试连接'按钮获取可用模型列表">
                        <InfoCircleOutlined style={{ marginLeft: 8 }} />
                      </Tooltip>
                    </span>
                  }
                >
                  <Select
                    placeholder="请先测试连接以获取模型列表"
                    loading={anthropicModelsLoading}
                    onChange={handleAnthropicModelSelect}
                    disabled={anthropicModels.length === 0 && !anthropicModelsLoading}
                    notFoundContent={anthropicModelsLoading ? <Spin size="small" /> : "请先测试连接"}
                    optionLabelProp="label"
                  >
                    {anthropicModels.map(model => (
                      <Option key={model.id} value={model.id} label={model.id}>
                        <div>
                          <div style={{ fontWeight: 'bold' }}>{model.id}</div>
                          {model.display_name && model.display_name !== model.id && (
                            <div style={{ fontSize: '12px', color: '#666' }}>
                              显示名称: {model.display_name}
                            </div>
                          )}
                        </div>
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>
          )}

          {/* Google模型选择 */}
          {currentProvider === 'google' && (
            <Row gutter={24}>
              <Col span={24}>
                <Form.Item
                  label={
                    <span>
                      选择Google模型
                      <Tooltip title="输入API密钥后点击'测试连接'按钮获取可用模型列表">
                        <InfoCircleOutlined style={{ marginLeft: 8 }} />
                      </Tooltip>
                    </span>
                  }
                >
                  <Select
                    placeholder="请先测试连接以获取模型列表"
                    loading={googleModelsLoading}
                    onChange={handleGoogleModelSelect}
                    disabled={googleModels.length === 0 && !googleModelsLoading}
                    notFoundContent={googleModelsLoading ? <Spin size="small" /> : "请先测试连接"}
                    optionLabelProp="label"
                  >
                    {googleModels.map(model => (
                      <Option key={model.name} value={model.name} label={model.displayName || model.baseModelId || model.name}>
                        <div>
                          <div style={{ fontWeight: 'bold' }}>{model.displayName || model.baseModelId || model.name}</div>
                          {model.description && (
                            <div style={{ fontSize: '12px', color: '#666' }}>
                              {model.description}
                            </div>
                          )}
                          {model.inputTokenLimit && (
                            <div style={{ fontSize: '12px', color: '#999' }}>
                              输入限制: {model.inputTokenLimit.toLocaleString()} tokens
                              {model.outputTokenLimit && ` | 输出限制: ${model.outputTokenLimit.toLocaleString()} tokens`}
                            </div>
                          )}
                        </div>
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>
          )}

          {/* X.ai模型选择 */}
          {currentProvider === 'xai' && (
            <Row gutter={24}>
              <Col span={24}>
                <Form.Item
                  label={
                    <span>
                      选择X.ai模型
                      <Tooltip title="输入API密钥后点击'测试连接'按钮获取可用模型列表">
                        <InfoCircleOutlined style={{ marginLeft: 8 }} />
                      </Tooltip>
                    </span>
                  }
                >
                  <Select
                    placeholder="请先测试连接以获取模型列表"
                    loading={xaiModelsLoading}
                    onChange={handleXaiModelSelect}
                    disabled={xaiModels.length === 0 && !xaiModelsLoading}
                    notFoundContent={xaiModelsLoading ? <Spin size="small" /> : "请先测试连接"}
                    optionLabelProp="label"
                  >
                    {xaiModels.map(model => (
                      <Option key={model.id} value={model.id} label={model.id}>
                        <div>
                          <div style={{ fontWeight: 'bold' }}>{model.id}</div>
                          {model.object && (
                            <div style={{ fontSize: '12px', color: '#666' }}>
                              类型: {model.object}
                            </div>
                          )}
                          {model.owned_by && (
                            <div style={{ fontSize: '12px', color: '#999' }}>
                              提供商: {model.owned_by}
                            </div>
                          )}
                        </div>
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>
          )}

          {/* 模型名称和ID */}
          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="名称"
                rules={[{ required: true, message: '请输入模型配置名称' }]}
              >
                <Input placeholder="例如: GPT-4 Turbo" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="model_id"
                label="模型ID"
                rules={[{ required: true, message: '请输入模型ID' }]}
                tooltip="模型的唯一标识符，如gpt-4-turbo-preview, claude-3-opus-20240229"
              >
                <Input
                  placeholder="例如: gpt-4-turbo-preview"
                  disabled={currentProvider === 'ollama' || currentProvider === 'gpustack' || currentProvider === 'anthropic' || currentProvider === 'google' || currentProvider === 'xai'}
                />
              </Form.Item>
            </Col>
          </Row>

          {currentProvider !== 'ollama' && (
            <Row gutter={24}>
              <Col span={24}>
                <Form.Item
                  name="apiKey"
                  label={
                    <span>
                      API密钥
                      <Tooltip title={
                        editingModel
                          ? "留空表示保持原有密钥不变"
                          : currentProvider === 'custom'
                            ? "自定义提供商的API密钥（可选）"
                            : "模型服务的API密钥"
                      }>
                        <InfoCircleOutlined style={{ marginLeft: 8 }} />
                      </Tooltip>
                    </span>
                  }
                  rules={[
                    {
                      required: !editingModel && currentProvider !== 'custom',
                      message: '请输入API密钥'
                    }
                  ]}
                >
                  <Input
                    placeholder={
                      editingModel
                        ? "留空表示保持原有密钥不变"
                        : currentProvider === 'custom'
                          ? "输入API密钥（可选）"
                          : "输入API密钥"
                    }
                    type={apiKeyVisible ? "text" : "password"}
                    addonAfter={
                      <Button
                        type="text"
                        icon={apiKeyVisible ? <EyeInvisibleOutlined /> : <EyeOutlined />}
                        onClick={() => setApiKeyVisible(!apiKeyVisible)}
                        style={{ border: 'none', padding: 0, height: 'auto' }}
                      />
                    }
                  />
                </Form.Item>
              </Col>
            </Row>
          )}

          <div style={{ marginTop: '24px', marginBottom: '16px' }}>
            <Title level={5}>模型参数</Title>
          </div>

          <Row gutter={24}>
            <Col span={8}>
              <Form.Item
                name="contextWindow"
                label="上下文窗口大小"
                rules={[{ required: true, message: '请输入上下文窗口大小' }]}
                tooltip="模型能处理的最大token数量，最小值为1"
              >
                <InputNumber min={1} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="maxOutputTokens"
                label="最大输出Token数"
                rules={[{ required: true, message: '请输入最大输出Token数' }]}
                tooltip="模型单次回复的最大token数量，最小值为1"
              >
                <InputNumber min={1} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="requestTimeout"
                label="请求超时(秒)"
                rules={[{ required: true, message: '请输入请求超时时间' }]}
                tooltip="API请求的超时时间，范围10-300秒"
              >
                <InputNumber min={10} max={300} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                name="modalities"
                label="模型模态"
                rules={[{ required: true, message: '请选择至少一项模型模态' }]}
                tooltip="模型支持的输入输出类型"
              >
                <Select
                  mode="multiple"
                  placeholder="选择模型模态"
                  style={{ width: '100%' }}
                  maxTagCount="responsive"
                  allowClear
                  showSearch={false}
                  options={MODEL_MODALITIES.map(mod => ({
                    label: (
                      <span>
                        <Tag color={mod.color}>
                          <span style={{ marginRight: 4 }}>{mod.icon}</span>
                          {mod.label}
                        </Tag>
                        <span style={{ marginLeft: 8 }}>{mod.description}</span>
                      </span>
                    ),
                    value: mod.value
                  }))}
                  tagRender={(props) => {
                    const { value, closable, onClose } = props;
                    const modality = MODEL_MODALITIES.find(mod => mod.value === value);
                    return (
                      <Tag
                        color={modality?.color || 'default'}
                        closable={closable}
                        onClose={onClose}
                        style={{ marginRight: 3, marginBottom: 3 }}
                      >
                        <span style={{ marginRight: 2 }}>{modality?.icon}</span>
                        {modality?.label || value}
                      </Tag>
                    );
                  }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="capabilities"
                label="模型特性"
                rules={[]}
                tooltip="模型支持的特性类型"
              >
                <Select
                  mode="multiple"
                  placeholder="选择模型特性"
                  style={{ width: '100%' }}
                  maxTagCount="responsive"
                  allowClear
                  showSearch={false}
                  options={MODEL_CAPABILITIES.map(cap => ({
                    label: (
                      <span>
                        <Tag color={cap.color}>
                          <span style={{ marginRight: 4 }}>{cap.icon}</span>
                          {cap.label}
                        </Tag>
                        <span style={{ marginLeft: 8 }}>{cap.description}</span>
                      </span>
                    ),
                    value: cap.value
                  }))}
                  tagRender={(props) => {
                    const { value, closable, onClose } = props;
                    const capability = MODEL_CAPABILITIES.find(cap => cap.value === value);
                    return (
                      <Tag
                        color={capability?.color || 'default'}
                        closable={closable}
                        onClose={onClose}
                        style={{ marginRight: 3, marginBottom: 3 }}
                      >
                        <span style={{ marginRight: 2 }}>{capability?.icon}</span>
                        {capability?.label || value}
                      </Tag>
                    );
                  }}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={24}>
              <Form.Item
                name="additionalParams"
                label="附加参数(JSON)"
                tooltip="以JSON格式提供的附加参数，将与API请求一起发送"
              >
                <TextArea
                  rows={4}
                  placeholder='{"frequency_penalty": 0, "presence_penalty": 0}'
                  style={{ fontFamily: 'monospace' }}
                />
              </Form.Item>
            </Col>
          </Row>


        </Form>
      </Modal>

      {/* 设置默认模型Modal */}
      <Modal
        title="设置默认模型"
        open={defaultModelModalVisible}
        onOk={handleDefaultModelOk}
        onCancel={handleDefaultModelCancel}
        confirmLoading={defaultModelLoading}
        width={600}
        destroyOnClose={true}
      >
        <Form
          form={defaultModelForm}
          layout="vertical"
          style={{ marginTop: '16px' }}
        >
          <Alert
            message="设置系统默认使用的模型"
            description="文本生成模型用于对话和文本生成，嵌入模型用于向量化和语义搜索。您可以分别设置这两种类型的默认模型。"
            type="info"
            showIcon
            style={{ marginBottom: '24px' }}
          />

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                name="textModelId"
                label="默认文本生成模型"
                tooltip="用于对话、文本生成等任务的默认模型"
              >
                <Select
                  placeholder="选择文本生成模型"
                  allowClear
                  showSearch
                  filterOption={(input, option) =>
                    option?.label?.toLowerCase().includes(input.toLowerCase())
                  }
                  options={modelConfigs
                    .filter(model => {
                      const modalities = model.modalities || [];
                      return modalities.includes('text_output');
                    })
                    .map(model => ({
                      value: model.id,
                      label: `${model.name} (${PROVIDER_NAMES[model.provider] || model.provider})`,
                      model: model
                    }))
                  }
                  optionRender={(option) => (
                    <div>
                      <div style={{ fontWeight: 'bold' }}>{option.data.model.name}</div>
                      <div style={{ fontSize: '12px', color: '#666' }}>
                        {PROVIDER_NAMES[option.data.model.provider] || option.data.model.provider} - {option.data.model.model_id}
                      </div>
                    </div>
                  )}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="embeddingModelId"
                label="默认嵌入模型"
                tooltip="用于向量化、语义搜索等任务的默认模型"
              >
                <Select
                  placeholder="选择嵌入模型"
                  allowClear
                  showSearch
                  filterOption={(input, option) =>
                    option?.label?.toLowerCase().includes(input.toLowerCase())
                  }
                  options={modelConfigs
                    .filter(model => {
                      const modalities = model.modalities || [];
                      return modalities.includes('vector_output');
                    })
                    .map(model => ({
                      value: model.id,
                      label: `${model.name} (${PROVIDER_NAMES[model.provider] || model.provider})`,
                      model: model
                    }))
                  }
                  optionRender={(option) => (
                    <div>
                      <div style={{ fontWeight: 'bold' }}>{option.data.model.name}</div>
                      <div style={{ fontSize: '12px', color: '#666' }}>
                        {PROVIDER_NAMES[option.data.model.provider] || option.data.model.provider} - {option.data.model.model_id}
                      </div>
                    </div>
                  )}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                name="rerankModelId"
                label="默认重排序模型"
                tooltip="用于搜索结果重排序、提升检索准确性的默认模型"
              >
                <Select
                  placeholder="选择重排序模型"
                  allowClear
                  showSearch
                  filterOption={(input, option) =>
                    option?.label?.toLowerCase().includes(input.toLowerCase())
                  }
                  options={modelConfigs
                    .filter(model => {
                      const modalities = model.modalities || [];
                      return modalities.includes('rerank_output');
                    })
                    .map(model => ({
                      value: model.id,
                      label: `${model.name} (${PROVIDER_NAMES[model.provider] || model.provider})`,
                      model: model
                    }))
                  }
                  optionRender={(option) => (
                    <div>
                      <div style={{ fontWeight: 'bold' }}>{option.data.model.name}</div>
                      <div style={{ fontSize: '12px', color: '#666' }}>
                        {PROVIDER_NAMES[option.data.model.provider] || option.data.model.provider} - {option.data.model.model_id}
                      </div>
                    </div>
                  )}
                />
              </Form.Item>
            </Col>
          </Row>

          {/* 显示当前默认模型信息 */}
          {(currentDefaults.text_model || currentDefaults.embedding_model || currentDefaults.rerank_model) && (
            <div style={{ marginTop: '16px', padding: '12px', backgroundColor: '#f5f5f5', borderRadius: '6px' }}>
              <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>当前默认模型：</div>
              {currentDefaults.text_model && (
                <div style={{ marginBottom: '4px' }}>
                  <Text type="secondary">文本生成：</Text>
                  <Text>{currentDefaults.text_model.name} ({PROVIDER_NAMES[currentDefaults.text_model.provider] || currentDefaults.text_model.provider})</Text>
                </div>
              )}
              {currentDefaults.embedding_model && (
                <div style={{ marginBottom: '4px' }}>
                  <Text type="secondary">嵌入模型：</Text>
                  <Text>{currentDefaults.embedding_model.name} ({PROVIDER_NAMES[currentDefaults.embedding_model.provider] || currentDefaults.embedding_model.provider})</Text>
                </div>
              )}
              {currentDefaults.rerank_model && (
                <div>
                  <Text type="secondary">重排序模型：</Text>
                  <Text>{currentDefaults.rerank_model.name} ({PROVIDER_NAMES[currentDefaults.rerank_model.provider] || currentDefaults.rerank_model.provider})</Text>
                </div>
              )}
            </div>
          )}
        </Form>
      </Modal>
    </div>
  );
};

export default ModelConfigsPage;
