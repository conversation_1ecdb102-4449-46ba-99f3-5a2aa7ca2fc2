import api from './axios';

const graphEnhancementAPI = {
  // 获取配置
  getConfig: async () => {
    const response = await api.get('/graph-enhancement/config');
    return response.data;
  },

  // 保存配置
  saveConfig: async (data) => {
    const response = await api.post('/graph-enhancement/config', data);
    return response.data;
  },

  // 获取状态
  getStatus: async () => {
    const response = await api.get('/graph-enhancement/status');
    return response.data;
  },



  // 测试查询
  testQuery: async (data) => {
    // 根据是否为高级模式选择不同的端点
    if (data.advanced_mode) {
      const response = await api.post('/graph-enhancement/test-advanced-query', data);
      return response.data;
    } else {
      const response = await api.post('/graph-enhancement/test-query', data);
      return response.data;
    }
  },

  // 高级测试查询
  testAdvancedQuery: async (data) => {
    const response = await api.post('/graph-enhancement/test-advanced-query', data);
    return response.data;
  },

  // 重建索引
  rebuildIndex: async () => {
    const response = await api.post('/graph-enhancement/rebuild-index');
    return response.data;
  },

  // 清空图谱数据
  clearGraph: async () => {
    const response = await api.post('/graph-enhancement/clear-graph');
    return response.data;
  },

  // 服务控制
  controlService: async (data) => {
    const response = await api.post('/graph-enhancement/service-control', data);
    return response.data;
  },
};

export default graphEnhancementAPI;