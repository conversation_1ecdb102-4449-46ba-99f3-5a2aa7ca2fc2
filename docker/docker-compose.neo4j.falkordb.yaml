services:
  neo4j:
    image: neo4j:latest
    container_name: neo4j
    ports:
      - "7474:7474"  # HTTP
      - "7687:7687"  # Bolt
    volumes:
      - neo4j_data:/data
    #environment: # default neo4j/neo4j
    #  - NEO4J_AUTH=none
    restart: unless-stopped
    networks:
      - graph_network

  falkordb:
    image: falkordb/falkordb:latest
    container_name: falkordb
    ports:
      - "6379:6379"  
      - "6380:3000"  # FalkorDB Browser
    volumes:
      - falkordb_data:/data
    environment:
      - REDIS_ARGS=--save 60 1000
    restart: unless-stopped
    networks:
      - graph_network

volumes:
  neo4j_data:
  falkordb_data:

networks:
  graph_network:
    driver: bridge
